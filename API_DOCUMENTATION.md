# Telegram Proxy Service API Documentation

## Overview
The Telegram Proxy Service provides a secure, rate-limited proxy for the Telegram Bot API with authentication, monitoring, and webhook support.

## Base URL
```
https://your-proxy-domain.com
```

## Authentication
All requests require an API key provided via:
- Header: `X-API-Key: your_api_key`
- Query parameter: `?api_key=your_api_key`
- Authorization header: `Authorization: Bearer your_api_key`

## URL Format

### API URL Format
```
https://your-proxy.com/bot{TOKEN}/{METHOD}
```

All Telegram Bot API requests use this clean, simple format.

## API Endpoints

### Telegram Bot API Proxy

#### Get Bot Information
```http
GET /bot{TOKEN}/getMe
```

**Example:**
```bash
curl -H "X-API-Key: your_api_key" \
  https://your-proxy.com/bot123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11/getMe
```

#### Send Message
```http
POST /bot{TOKEN}/sendMessage
Content-Type: application/json
```

**Example:**
```bash
curl -X POST \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"chat_id": "123456789", "text": "Hello World!"}' \
  https://your-proxy.com/bot123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11/sendMessage
```

#### Send Photo
```http
POST /bot{TOKEN}/sendPhoto
Content-Type: multipart/form-data
```

**Example:**
```bash
curl -X POST \
  -H "X-API-Key: your_api_key" \
  -F "chat_id=123456789" \
  -F "photo=@image.jpg" \
  https://your-proxy.com/bot123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11/sendPhoto
```

#### Get Updates
```http
GET /bot{TOKEN}/getUpdates
```

**Example:**
```bash
curl -H "X-API-Key: your_api_key" \
  https://your-proxy.com/bot123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11/getUpdates
```

### Webhook Management

#### Setup Webhook
```http
POST /api/v1.0/webhook/setup
Content-Type: application/json
X-API-Key: your_api_key
```

**Request Body:**
```json
{
  "botToken": "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11",
  "targetUrl": "https://your-bot.com/webhook",
  "secretToken": "optional_secret_token",
  "maxConnections": 40,
  "allowedUpdates": ["message", "callback_query"],
  "dropPendingUpdates": false
}
```

#### Remove Webhook
```http
POST /api/v1.0/webhook/remove
Content-Type: application/json
X-API-Key: your_api_key
```

**Request Body:**
```json
{
  "botToken": "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11"
}
```

#### List Webhooks
```http
GET /api/v1.0/webhook/list
X-API-Key: your_api_key
```

#### Get Webhook Info
```http
GET /api/v1.0/webhook/info/{botToken}
X-API-Key: your_api_key
```

### System Endpoints

#### Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "checks": {
    "cpu": "healthy",
    "memory": "healthy",
    "errorRate": "healthy"
  },
  "timestamp": "2025-05-28T10:00:00.000Z"
}
```

#### Metrics
```http
GET /metrics
```

#### Prometheus Metrics
```http
GET /metrics/prometheus
```

## Rate Limits

### Global Rate Limits
- **100 requests per minute** per IP address
- **60 requests per minute** per API key

### Rate Limit Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Error Responses

### Authentication Errors
```json
{
  "code": 401,
  "message": {
    "head": "Authentication Required",
    "body": "API key is required to access the proxy service."
  },
  "error": "API key is required."
}
```

### Rate Limit Exceeded
```json
{
  "code": 429,
  "message": {
    "head": "Rate Limit Exceeded",
    "body": "Too many requests from your API key."
  },
  "error": "Rate limit exceeded. Try again in 60 seconds.",
  "retryAfter": 60
}
```

### Proxy Errors
```json
{
  "code": 500,
  "message": {
    "head": "Proxy Error",
    "body": "Error occurred while proxying request to Telegram."
  },
  "error": "Telegram API request failed."
}
```

## SDK Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

const telegramProxy = axios.create({
  baseURL: 'https://your-proxy.com',
  headers: {
    'X-API-Key': 'your_api_key'
  }
});

// Send message
await telegramProxy.post('/bot123456:TOKEN/sendMessage', {
  chat_id: '123456789',
  text: 'Hello from proxy!'
});

// Get updates
const updates = await telegramProxy.get('/bot123456:TOKEN/getUpdates');
```

### Python
```python
import requests

class TelegramProxy:
    def __init__(self, api_key, bot_token):
        self.base_url = 'https://your-proxy.com'
        self.headers = {'X-API-Key': api_key}
        self.bot_token = bot_token

    def send_message(self, chat_id, text):
        url = f'{self.base_url}/bot{self.bot_token}/sendMessage'
        data = {'chat_id': chat_id, 'text': text}
        return requests.post(url, json=data, headers=self.headers)

    def get_updates(self):
        url = f'{self.base_url}/bot{self.bot_token}/getUpdates'
        return requests.get(url, headers=self.headers)

# Usage
proxy = TelegramProxy('your_api_key', '123456:TOKEN')
proxy.send_message('123456789', 'Hello!')
```

### PHP
```php
<?php
class TelegramProxy {
    private $baseUrl = 'https://your-proxy.com';
    private $apiKey;
    private $botToken;

    public function __construct($apiKey, $botToken) {
        $this->apiKey = $apiKey;
        $this->botToken = $botToken;
    }

    public function sendMessage($chatId, $text) {
        $url = $this->baseUrl . '/bot' . $this->botToken . '/sendMessage';
        $data = json_encode(['chat_id' => $chatId, 'text' => $text]);

        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'X-API-Key: ' . $this->apiKey
                ],
                'content' => $data
            ]
        ]);

        return file_get_contents($url, false, $context);
    }
}

// Usage
$proxy = new TelegramProxy('your_api_key', '123456:TOKEN');
$proxy->sendMessage('123456789', 'Hello!');
?>
```

## Migration Guide

### From Legacy URLs to Short URLs

**Old Format:**
```
https://your-proxy.com/api/v1.0/proxy/bot123456:TOKEN/sendMessage
```

**New Format:**
```
https://your-proxy.com/bot123456:TOKEN/sendMessage
```

### Benefits of Short URLs
- **24% shorter URLs** (15+ characters saved)
- **Cleaner API interface**
- **Easier to remember and type**
- **Same security and features**

### Backward Compatibility
- Legacy URLs continue to work
- No breaking changes
- Same authentication required
- Same rate limits apply

## Best Practices

### Security
1. **Keep API keys secure** - Never expose in client-side code
2. **Use HTTPS only** - All requests must use HTTPS
3. **Rotate API keys regularly** - Update keys periodically
4. **Monitor usage** - Check metrics and alerts

### Performance
1. **Implement caching** - Cache responses when appropriate
2. **Handle rate limits** - Implement exponential backoff
3. **Use webhooks** - More efficient than polling getUpdates
4. **Monitor metrics** - Track response times and error rates

### Error Handling
1. **Check status codes** - Handle different error types
2. **Implement retries** - With exponential backoff
3. **Log errors** - For debugging and monitoring
4. **Graceful degradation** - Handle service unavailability

## Support

For technical support or questions:
- Check system status: `GET /health`
- View metrics: `GET /metrics`
- Monitor alerts: `GET /alerts`

## Changelog

### v1.1.0 - Short URL Support
- ✅ Added short URL format: `/bot{TOKEN}/{METHOD}`
- ✅ Maintained backward compatibility
- ✅ Same security and rate limiting
- ✅ 24% shorter URLs

### v1.0.0 - Initial Release
- ✅ Telegram Bot API proxy
- ✅ API key authentication
- ✅ Rate limiting
- ✅ Webhook support
- ✅ Monitoring and alerting
