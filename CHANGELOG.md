# Changelog

All notable changes to the Telegram Proxy Service will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2025-05-29

### 🚨 BREAKING CHANGES
- **Removed legacy URL formats** - Only short URL format is now supported
- **API URL format simplified** - All requests must use `/bot{TOKEN}/{METHOD}` format

### ❌ Removed
- Legacy URL format: `/api/v1.0/proxy/bot{TOKEN}/{METHOD}` (returns 404)
- Legacy URL format: `/api/v1.0/bot{TOKEN}/{METHOD}` (returns 404)
- Legacy webhook URLs: `/api/v1.0/webhook/*` (returns 404)

### ✅ Current Formats
- **Bot API format**: `/bot{TOKEN}/{METHOD}` (only supported format)
- **Webhook management**: `/webhook/*` (simplified format)

### 📈 Benefits
- **24% shorter URLs** - Save 15+ characters per request
- **Cleaner API interface** - More intuitive and easier to remember
- **Simplified codebase** - Easier maintenance and fewer edge cases
- **Better performance** - No legacy route processing overhead

### 🔧 Migration Required
All existing integrations must update their URLs:

**Before (NO LONGER WORKS):**
```
❌ https://your-proxy.com/api/v1.0/proxy/bot123456:TOKEN/getMe
❌ https://your-proxy.com/api/v1.0/bot123456:TOKEN/getMe
❌ https://your-proxy.com/api/v1.0/webhook/setup
```

**After (REQUIRED):**
```
✅ https://your-proxy.com/bot123456:TOKEN/getMe
✅ https://your-proxy.com/webhook/setup
```

### 🔒 Security & Features Unchanged
- ✅ Same API key authentication required
- ✅ Same rate limiting (100 req/min per IP, 60 req/min per API key)
- ✅ Same access control and security measures
- ✅ Same webhook functionality (just shorter URLs)
- ✅ Same monitoring and alerting
- ✅ Same error handling and logging

### 📚 Documentation Updated
- Updated API documentation with new format only
- Updated README with current examples
- Updated migration guide with breaking change notice
- Updated all test scripts

---

## [1.1.0] - 2025-05-28

### ✨ Added
- **Short URL support** - New `/bot{TOKEN}/{METHOD}` format alongside legacy URLs
- **Webhook management** - Complete webhook setup, removal, and monitoring
- **Access control** - IP/Domain/User blacklisting and whitelisting
- **Monitoring & alerting** - Real-time metrics and multi-channel alerts
- **Prometheus metrics** - Industry-standard metrics export
- **Health checks** - Comprehensive system health monitoring

### 🔧 Enhanced
- **Rate limiting** - Multi-level rate limiting with Redis backend
- **Usage analytics** - Detailed usage statistics and reporting
- **Error handling** - Improved error categorization and logging
- **Admin tools** - Enhanced API key management scripts

### 📊 Monitoring Features
- System metrics (CPU, memory, load average)
- Application metrics (requests, errors, response times)
- Proxy-specific metrics (rate limits, auth failures)
- Alert system with Slack/email/webhook notifications
- Alert history and statistics

### 🛡️ Security Features
- Multi-scope access control (global, API key, user)
- CIDR notation support for IP ranges
- Auto-detection of malicious behavior
- Comprehensive audit logging

### 🔗 Webhook Features
- Complete webhook lifecycle management
- Delivery retry with exponential backoff
- Health monitoring and statistics
- Secret token verification
- Webhook delivery analytics

---

## [1.0.0] - 2025-05-27

### ✨ Initial Release
- **Telegram Bot API proxy** - Complete proxy functionality
- **API key authentication** - Secure access control
- **Rate limiting** - Basic rate limiting per IP and API key
- **Request logging** - Comprehensive request/response logging
- **Error handling** - Proper error responses and logging
- **Health endpoints** - Basic health check functionality

### 🔧 Core Features
- RESTful API structure with versioning
- MongoDB integration for data persistence
- Redis integration for rate limiting and caching
- Express.js framework with security middleware
- Structured logging with Winston
- Clean project organization

### 🔒 Security
- API key based authentication
- Request validation and sanitization
- Security headers with Helmet.js
- CORS configuration
- Input validation

### 📝 API Endpoints
- `/api/v1.0/proxy/*` - Telegram Bot API proxy
- `/health` - Health check endpoint
- User and authentication management

### 🛠️ Management Tools
- API key creation and management scripts
- User management functionality
- Usage statistics and reporting

---

## Migration Notes

### From v1.x to v2.0
**BREAKING CHANGE**: Legacy URL formats removed.

**Required Actions:**
1. Update all API calls to use short URL format: `/bot{TOKEN}/{METHOD}`
2. Remove `/api/v1.0/proxy/` and `/api/v1.0/` prefixes from URLs
3. Test all integrations with new format
4. Update documentation and examples

**No Changes Required:**
- API keys remain the same
- Authentication methods unchanged
- Rate limits and security measures identical
- Webhook management endpoints unchanged
- All other functionality preserved

### Testing Migration
Use the provided test script to verify migration:
```bash
node test-legacy-removal.js
```

This will verify:
- Legacy URLs return 404
- Current URLs work correctly
- Authentication still enforced
- All security measures intact
