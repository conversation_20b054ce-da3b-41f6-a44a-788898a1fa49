# Telegram Proxy Service - Production Deployment Guide

## 🚀 Triển khai Production

### <PERSON><PERSON><PERSON> c<PERSON>u hệ thống

**Minimum Requirements:**
- Node.js 18+ 
- MongoDB 4.4+
- Redis 6.0+
- RAM: 2GB+
- CPU: 2 cores+
- Storage: 20GB+

**Recommended for Production:**
- Node.js 20 LTS
- MongoDB 6.0+
- Redis 7.0+
- RAM: 8GB+
- CPU: 4 cores+
- Storage: 100GB+ SSD

### 1. <PERSON><PERSON>n bị môi trường

```bash
# Cập nhật hệ thống
sudo apt update && sudo apt upgrade -y

# Cài đặt Node.js 20 LTS
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Cài đặt MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Cài đặt Redis
sudo apt install redis-server -y

# Cài đặt PM2 cho process management
sudo npm install -g pm2
```

### 2. Cấu hình Database

**MongoDB Configuration:**
```bash
# Tạo user admin cho MongoDB
mongo
> use admin
> db.createUser({
    user: "admin",
    pwd: "your_secure_password",
    roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase", "readWriteAnyDatabase"]
})

# Tạo database cho proxy service
> use telegram-proxy
> db.createUser({
    user: "proxy_user",
    pwd: "proxy_secure_password",
    roles: ["readWrite"]
})
```

**Redis Configuration:**
```bash
# Chỉnh sửa /etc/redis/redis.conf
sudo nano /etc/redis/redis.conf

# Thêm/sửa các dòng sau:
requirepass your_redis_password
maxmemory 1gb
maxmemory-policy allkeys-lru
```

### 3. Cấu hình ứng dụng

**Tạo production config:**
```bash
cp config-template.json config/production.json
```

**Chỉnh sửa config/production.json:**
```json
{
  "redis": {
    "connections": {
      "master": {
        "host": "localhost",
        "port": 6379,
        "database": 0,
        "password": "your_redis_password"
      }
    }
  },
  "mongo": {
    "connections": {
      "master": {
        "host": "localhost",
        "port": 27017,
        "database": "telegram-proxy",
        "options": {
          "useUnifiedTopology": true,
          "useNewUrlParser": true,
          "user": "proxy_user",
          "pass": "proxy_secure_password"
        }
      }
    }
  },
  "port": 3000,
  "logLevel": "info",
  "serviceName": "TELEGRAM-PROXY-SERVICE",
  "secretKey": "your_very_secure_secret_key_here",
  "proxy": {
    "telegramApiUrl": "https://api.telegram.org",
    "telegramFileApiUrl": "https://api.telegram.org/file",
    "timeout": 30000,
    "maxRetries": 2,
    "retryDelay": 1000,
    "maxRequestSize": 52428800,
    "rateLimits": {
      "global": {
        "requestsPerMinute": 1000,
        "blockDuration": 60
      },
      "default": {
        "requestsPerMinute": 100,
        "requestsPerHour": 5000,
        "dailyLimit": 50000,
        "monthlyLimit": 1000000
      }
    }
  }
}
```

### 4. SSL/TLS Configuration

**Sử dụng Nginx làm reverse proxy:**

```bash
# Cài đặt Nginx
sudo apt install nginx -y

# Cài đặt Certbot cho Let's Encrypt
sudo apt install certbot python3-certbot-nginx -y

# Tạo SSL certificate
sudo certbot --nginx -d your-domain.com
```

**Nginx configuration (/etc/nginx/sites-available/telegram-proxy):**
```nginx
upstream telegram_proxy {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;  # Nếu chạy multiple instances
    server 127.0.0.1:3002;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    location / {
        proxy_pass http://telegram_proxy;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://telegram_proxy;
        access_log off;
    }
}
```

### 5. Process Management với PM2

**Tạo ecosystem.config.js:**
```javascript
module.exports = {
  apps: [{
    name: 'telegram-proxy',
    script: 'index.js',
    instances: 'max', // Sử dụng tất cả CPU cores
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    // Restart policies
    max_restarts: 10,
    min_uptime: '10s',
    max_memory_restart: '1G',
    
    // Logging
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Monitoring
    monitoring: false
  }]
};
```

**Khởi động service:**
```bash
# Cài đặt dependencies
npm ci --only=production

# Khởi động với PM2
pm2 start ecosystem.config.js --env production

# Lưu PM2 configuration
pm2 save

# Tự động khởi động khi reboot
pm2 startup
```

### 6. Monitoring và Logging

**Cài đặt monitoring tools:**
```bash
# PM2 monitoring
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30

# System monitoring
sudo apt install htop iotop nethogs -y
```

**Log rotation:**
```bash
# Tạo logrotate config
sudo nano /etc/logrotate.d/telegram-proxy

# Nội dung:
/path/to/your/app/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 www-data www-data
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 7. Security Hardening

**Firewall configuration:**
```bash
# Cấu hình UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

**Fail2ban cho protection:**
```bash
sudo apt install fail2ban -y

# Tạo jail cho Nginx
sudo nano /etc/fail2ban/jail.local

[nginx-req-limit]
enabled = true
filter = nginx-req-limit
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
findtime = 600
bantime = 7200
maxretry = 10
```

### 8. Backup Strategy

**Database backup script:**
```bash
#!/bin/bash
# backup-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/telegram-proxy"
mkdir -p $BACKUP_DIR

# MongoDB backup
mongodump --host localhost --port 27017 --db telegram-proxy --out $BACKUP_DIR/mongo_$DATE

# Redis backup
cp /var/lib/redis/dump.rdb $BACKUP_DIR/redis_$DATE.rdb

# Compress backups
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz $BACKUP_DIR/mongo_$DATE $BACKUP_DIR/redis_$DATE.rdb

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +30 -delete

echo "Backup completed: backup_$DATE.tar.gz"
```

**Crontab cho auto backup:**
```bash
# Chạy backup hàng ngày lúc 2:00 AM
0 2 * * * /path/to/backup-db.sh
```

### 9. Performance Tuning

**Node.js optimization:**
```bash
# Tăng memory limit
export NODE_OPTIONS="--max-old-space-size=4096"

# Enable production optimizations
export NODE_ENV=production
```

**MongoDB optimization:**
```javascript
// Tạo indexes cho performance
db.apikeys.createIndex({ "keyId": 1 })
db.apikeys.createIndex({ "userId": 1 })
db.apikeys.createIndex({ "status": 1 })
db.requestlogs.createIndex({ "timestamp": -1 })
db.requestlogs.createIndex({ "apiKeyId": 1, "timestamp": -1 })
```

### 10. Health Checks và Alerts

**Health check script:**
```bash
#!/bin/bash
# health-check.sh

ENDPOINT="https://your-domain.com/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $ENDPOINT)

if [ $RESPONSE -ne 200 ]; then
    echo "Service is down! HTTP $RESPONSE"
    # Send alert (email, Slack, etc.)
    # Restart service if needed
    pm2 restart telegram-proxy
fi
```

### 11. Deployment Checklist

- [ ] Database configured và secured
- [ ] Redis configured với password
- [ ] SSL certificates installed
- [ ] Nginx configured và tested
- [ ] PM2 ecosystem configured
- [ ] Firewall rules applied
- [ ] Backup strategy implemented
- [ ] Monitoring tools installed
- [ ] Health checks configured
- [ ] Log rotation setup
- [ ] Performance indexes created
- [ ] Security hardening applied

### 12. Maintenance Commands

```bash
# Xem logs
pm2 logs telegram-proxy

# Restart service
pm2 restart telegram-proxy

# Monitor performance
pm2 monit

# Update application
git pull
npm ci --only=production
pm2 reload telegram-proxy

# Database maintenance
mongo telegram-proxy --eval "db.requestlogs.deleteMany({timestamp: {$lt: new Date(Date.now() - 90*24*60*60*1000)}})"
```

## 🔧 Troubleshooting

**Common Issues:**

1. **High memory usage:** Tăng `max_memory_restart` trong PM2 config
2. **Database connection errors:** Kiểm tra MongoDB credentials và network
3. **Rate limiting issues:** Điều chỉnh Redis configuration
4. **SSL certificate renewal:** `sudo certbot renew`

**Performance Monitoring:**
- CPU usage: `htop`
- Memory usage: `free -h`
- Disk usage: `df -h`
- Network: `nethogs`
- Application: `pm2 monit`

Hệ thống Telegram Proxy Service đã sẵn sàng cho production! 🚀
