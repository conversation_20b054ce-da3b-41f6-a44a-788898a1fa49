# Breaking Changes: URL Format Simplification

## Overview
**BREAKING CHANGE**: We've simplified the Telegram Proxy Service to use only the clean, short URL format. Legacy URL formats are no longer supported.

## What's Changed

### Before (Legacy Formats - NO LONGER SUPPORTED)
```
❌ https://your-proxy.com/api/v1.0/proxy/bot{TOKEN}/{METHOD}
❌ https://your-proxy.com/api/v1.0/bot{TOKEN}/{METHOD}
```

### Now (Only Supported Format)
```
✅ https://your-proxy.com/bot{TOKEN}/{METHOD}
```

## Benefits of Simplified URLs

- **24% shorter URLs** - Save 15+ characters per request
- **Cleaner API interface** - More intuitive and easier to remember
- **Simplified codebase** - Easier maintenance and fewer edge cases
- **Better performance** - No legacy route processing overhead

## Migration Examples

### Basic Bot Methods

#### getMe
```bash
# Old format (NO LONGER WORKS)
❌ curl -H "X-API-Key: your_key" \
  https://your-proxy.com/api/v1.0/proxy/bot123456:TOKEN/getMe

# New format (REQUIRED)
✅ curl -H "X-API-Key: your_key" \
  https://your-proxy.com/bot123456:TOKEN/getMe
```

#### sendMessage
```bash
# Old format
curl -X POST -H "X-API-Key: your_key" \
  -d '{"chat_id":"123","text":"Hello"}' \
  https://your-proxy.com/api/v1.0/proxy/bot123456:TOKEN/sendMessage

# New format (recommended)
curl -X POST -H "X-API-Key: your_key" \
  -d '{"chat_id":"123","text":"Hello"}' \
  https://your-proxy.com/bot123456:TOKEN/sendMessage
```

#### sendPhoto
```bash
# Old format
curl -X POST -H "X-API-Key: your_key" \
  -F "chat_id=123" -F "photo=@image.jpg" \
  https://your-proxy.com/api/v1.0/proxy/bot123456:TOKEN/sendPhoto

# New format (recommended)
curl -X POST -H "X-API-Key: your_key" \
  -F "chat_id=123" -F "photo=@image.jpg" \
  https://your-proxy.com/bot123456:TOKEN/sendPhoto
```

## Code Migration Examples

### JavaScript/Node.js

#### Before
```javascript
const axios = require('axios');

const telegramProxy = axios.create({
  baseURL: 'https://your-proxy.com/api/v1.0/proxy',
  headers: { 'X-API-Key': 'your_api_key' }
});

// Send message
await telegramProxy.post('/bot123456:TOKEN/sendMessage', {
  chat_id: '123456789',
  text: 'Hello!'
});
```

#### After
```javascript
const axios = require('axios');

const telegramProxy = axios.create({
  baseURL: 'https://your-proxy.com',
  headers: { 'X-API-Key': 'your_api_key' }
});

// Send message
await telegramProxy.post('/bot123456:TOKEN/sendMessage', {
  chat_id: '123456789',
  text: 'Hello!'
});
```

### Python

#### Before
```python
import requests

class TelegramProxy:
    def __init__(self, api_key, bot_token):
        self.base_url = 'https://your-proxy.com/api/v1.0/proxy'
        self.headers = {'X-API-Key': api_key}
        self.bot_token = bot_token

    def send_message(self, chat_id, text):
        url = f'{self.base_url}/bot{self.bot_token}/sendMessage'
        data = {'chat_id': chat_id, 'text': text}
        return requests.post(url, json=data, headers=self.headers)
```

#### After
```python
import requests

class TelegramProxy:
    def __init__(self, api_key, bot_token):
        self.base_url = 'https://your-proxy.com'
        self.headers = {'X-API-Key': api_key}
        self.bot_token = bot_token

    def send_message(self, chat_id, text):
        url = f'{self.base_url}/bot{self.bot_token}/sendMessage'
        data = {'chat_id': chat_id, 'text': text}
        return requests.post(url, json=data, headers=self.headers)
```

### PHP

#### Before
```php
<?php
class TelegramProxy {
    private $baseUrl = 'https://your-proxy.com/api/v1.0/proxy';
    private $apiKey;
    private $botToken;

    public function sendMessage($chatId, $text) {
        $url = $this->baseUrl . '/bot' . $this->botToken . '/sendMessage';
        // ... rest of implementation
    }
}
?>
```

#### After
```php
<?php
class TelegramProxy {
    private $baseUrl = 'https://your-proxy.com';
    private $apiKey;
    private $botToken;

    public function sendMessage($chatId, $text) {
        $url = $this->baseUrl . '/bot' . $this->botToken . '/sendMessage';
        // ... rest of implementation
    }
}
?>
```

## Migration Strategy

### Option 1: Immediate Migration (Recommended)
Update all your code to use the new short URL format immediately. This gives you the benefits of shorter URLs right away.

### Option 2: Gradual Migration
1. Update new code to use short URLs
2. Gradually update existing code during maintenance
3. Both formats work simultaneously

### Option 3: No Migration Required
Continue using legacy URLs - they will continue to work indefinitely with no changes required.

## What Stays the Same

### Authentication
- Same API key authentication required
- Same headers: `X-API-Key`, `Authorization: Bearer`, or query parameter
- Same security measures

### Rate Limiting
- Same rate limits apply (100 req/min per IP, 60 req/min per API key)
- Same rate limit headers returned
- Same rate limit error responses

### Features
- All Telegram Bot API methods supported
- Same request/response formats
- Same error handling
- Same monitoring and logging

### Webhook Management
- Webhook endpoints unchanged: `/api/v1.0/webhook/*`
- Same webhook setup and management
- Same webhook delivery mechanisms

## Testing Your Migration

### 1. Test Basic Functionality
```bash
# Test with your actual bot token and API key
curl -H "X-API-Key: YOUR_API_KEY" \
  https://your-proxy.com/botYOUR_BOT_TOKEN/getMe
```

### 2. Test Different Methods
```bash
# Test POST request
curl -X POST -H "X-API-Key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"chat_id":"YOUR_CHAT_ID","text":"Test message"}' \
  https://your-proxy.com/botYOUR_BOT_TOKEN/sendMessage
```

### 3. Test Error Handling
```bash
# Test without API key (should return 401)
curl https://your-proxy.com/botYOUR_BOT_TOKEN/getMe
```

## Troubleshooting

### Common Issues

#### 1. 404 Not Found
- **Cause**: Using old URL format incorrectly
- **Solution**: Remove `/api/v1.0/proxy` from URL

#### 2. 401 Unauthorized
- **Cause**: Missing or invalid API key
- **Solution**: Ensure API key is included in headers

#### 3. Rate Limit Errors
- **Cause**: Same rate limits apply to both URL formats
- **Solution**: Implement proper rate limiting in your code

### Getting Help

1. **Check Service Status**: `GET https://your-proxy.com/health`
2. **View API Documentation**: See `API_DOCUMENTATION.md`
3. **Monitor Usage**: Use webhook management endpoints
4. **Contact Support**: Check system metrics and alerts

## FAQ

### Q: Do I need to update my API keys?
**A**: No, existing API keys work with both URL formats.

### Q: Will legacy URLs stop working?
**A**: No, legacy URLs will continue to work indefinitely for backward compatibility.

### Q: Are there any performance differences?
**A**: No, both URL formats have identical performance characteristics.

### Q: Do rate limits change?
**A**: No, the same rate limits apply to both URL formats.

### Q: What about webhook URLs?
**A**: Webhook management endpoints remain unchanged at `/api/v1.0/webhook/*`.

### Q: Can I mix URL formats?
**A**: Yes, you can use both formats simultaneously during migration.

## Summary

The new short URL format provides a cleaner, more intuitive API interface while maintaining full backward compatibility. You can migrate at your own pace or continue using legacy URLs indefinitely.

**Key Points:**
- ✅ 24% shorter URLs
- ✅ Same functionality and security
- ✅ Backward compatible
- ✅ No breaking changes
- ✅ Optional migration

Start using the new format today: `https://your-proxy.com/bot{TOKEN}/{METHOD}`
