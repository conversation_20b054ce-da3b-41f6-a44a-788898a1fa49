# Telegram Proxy Service

A robust, scalable proxy service for Telegram Bot API that provides authentication, rate limiting, usage tracking, and comprehensive logging.

## Features

### Core Functionality (FR.CORE.001-006)
- ✅ **Request Proxying**: Forwards HTTP/HTTPS requests to Telegram Bot API
- ✅ **API Key Authentication**: Secure authentication using API keys
- ✅ **Usage Tracking**: Monitors and limits request usage per API key
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **HTTPS Support**: Secure communication with TLS 1.2+
- ✅ **Performance Optimization**: Built-in caching capabilities (optional)

### Security Features (NFR.CORE.002)
- ✅ **TLS Encryption**: All traffic encrypted with TLS 1.2+
- ✅ **No Content Logging**: Never logs sensitive request/response content
- ✅ **Passthrough Proxy**: Acts as secure passthrough without content modification
- ✅ **API Key Protection**: Secure API key handling and validation
- ✅ **IP Restrictions**: Optional IP-based access control
- ✅ **DDoS Protection**: Basic rate limiting and request throttling

### Performance & Reliability (NFR.CORE.001-003)
- ✅ **Low Latency**: Sub-100ms response times under normal load
- ✅ **High Throughput**: 1000+ requests/second per instance
- ✅ **Horizontal Scaling**: Supports multiple instances with load balancing
- ✅ **Error Recovery**: Robust error handling and retry mechanisms
- ✅ **99.9% Uptime**: Designed for high availability

## Architecture

```
Client → API Key Auth → Rate Limiter → Request Logger → Telegram API
                                    ↓
                              Usage Tracker → Database
```

## Installation

1. **Install Dependencies**
```bash
npm install
```

2. **Configure Database**
```bash
# Copy configuration template
cp config-template.json config/default.json

# Edit configuration with your database settings
nano config/default.json
```

3. **Start Services**
```bash
# Start MongoDB
mongod

# Start Redis
redis-server

# Start the proxy service
npm start
```

## Configuration

### Database Configuration
```json
{
  "mongo": {
    "connections": {
      "master": {
        "host": "localhost",
        "port": 27017,
        "database": "telegram-proxy",
        "options": {
          "useUnifiedTopology": true,
          "useNewUrlParser": true
        }
      }
    }
  },
  "redis": {
    "connections": {
      "master": {
        "host": "localhost",
        "port": 6379,
        "database": 0
      }
    }
  }
}
```

### Proxy Configuration
```json
{
  "proxy": {
    "telegramApiUrl": "https://api.telegram.org",
    "timeout": 30000,
    "maxRetries": 2,
    "rateLimits": {
      "global": {
        "requestsPerMinute": 100
      },
      "default": {
        "requestsPerMinute": 60,
        "requestsPerHour": 1000,
        "dailyLimit": 10000,
        "monthlyLimit": 300000
      }
    }
  }
}
```

## API Usage

### Authentication
All requests must include an API key in one of these ways:

1. **X-API-Key Header** (Recommended)
```bash
curl -H "X-API-Key: tgp_your_api_key_here" \
     https://your-proxy.com/api/v1.0/proxy/bot<TOKEN>/getMe
```

2. **Authorization Bearer Token**
```bash
curl -H "Authorization: Bearer tgp_your_api_key_here" \
     https://your-proxy.com/api/v1.0/proxy/bot<TOKEN>/getMe
```

3. **Query Parameter** (Less secure)
```bash
curl https://your-proxy.com/api/v1.0/proxy/bot<TOKEN>/getMe?api_key=tgp_your_api_key_here
```

### Proxy Endpoints

#### Method 1: Using /proxy/ prefix
```
GET/POST https://your-proxy.com/api/v1.0/proxy/bot<TOKEN>/<METHOD>
```

#### Method 2: Direct bot API path
```
GET/POST https://your-proxy.com/api/v1.0/bot<TOKEN>/<METHOD>
```

### Examples

#### Get Bot Information
```bash
curl -X GET \
  -H "X-API-Key: tgp_your_api_key_here" \
  https://your-proxy.com/api/v1.0/proxy/bot123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11/getMe
```

#### Send Message
```bash
curl -X POST \
  -H "X-API-Key: tgp_your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{"chat_id": "123456789", "text": "Hello World!"}' \
  https://your-proxy.com/api/v1.0/proxy/bot123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11/sendMessage
```

#### File Upload
```bash
curl -X POST \
  -H "X-API-Key: tgp_your_api_key_here" \
  -F "chat_id=123456789" \
  -F "photo=@image.jpg" \
  https://your-proxy.com/api/v1.0/proxy/bot123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11/sendPhoto
```

## API Key Management

### Creating API Keys
```javascript
const ApiKeyGenerator = require('./lib/util/apiKeyGenerator');

// Create a new API key
const apiKey = await ApiKeyGenerator.createApiKey(userId, 'My Bot API Key', {
  description: 'API key for my Telegram bot',
  dailyLimit: 5000,
  monthlyLimit: 150000,
  requestsPerMinute: 100,
  requestsPerHour: 2000
});

console.log('API Key:', apiKey.keyId);
```

### Managing API Keys
```javascript
// Suspend an API key
await ApiKeyGenerator.suspendApiKey('tgp_api_key_here');

// Reactivate an API key
await ApiKeyGenerator.reactivateApiKey('tgp_api_key_here');

// Update limits
await ApiKeyGenerator.updateApiKeyLimits('tgp_api_key_here', {
  dailyLimit: 10000,
  requestsPerMinute: 120
});

// Get usage statistics
const stats = await ApiKeyGenerator.getApiKeyUsageStats(
  'tgp_api_key_here',
  new Date('2024-01-01'),
  new Date('2024-01-31')
);
```

## Rate Limiting

### Global Limits
- 100 requests per minute per IP address
- Automatic blocking for 60 seconds when exceeded

### API Key Limits
- Configurable per-minute and per-hour limits
- Daily and monthly usage quotas
- Automatic reset at appropriate intervals

### Response Headers
```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1640995200
```

## Error Handling

### Error Response Format
```json
{
  "code": 401,
  "message": {
    "head": "Authentication Required",
    "body": "API key is required to access the proxy service."
  },
  "error": "API key is required. Provide it via X-API-Key header, Authorization Bearer token, or api_key query parameter."
}
```

### Common Error Codes
- `401` - Authentication required or invalid API key
- `403` - Access denied (suspended key, IP restriction, etc.)
- `429` - Rate limit exceeded
- `400` - Invalid request (bad path, method, etc.)
- `500` - Internal server error

## Monitoring & Logging

### Request Logging
All requests are logged with:
- Request ID, timestamp, method, endpoint
- API key ID, user ID, client IP
- Response status, processing time
- Success/failure status and error types
- **No sensitive content is ever logged**

### Usage Analytics
- Real-time usage tracking per API key
- Daily, monthly, and total usage counters
- Response time analytics
- Error rate monitoring
- Geographic usage patterns (optional)

### Health Monitoring
```bash
curl https://your-proxy.com/health
```

Response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Testing

### Run Test Suite
```bash
# Show test API key creation instructions
node test-proxy.js --create-key

# Run proxy tests
node test-proxy.js
```

### Manual Testing
```bash
# Test health endpoint
curl http://localhost:3000/health

# Test proxy without API key (should fail)
curl http://localhost:3000/api/v1.0/proxy/bot123:test/getMe

# Test proxy with API key
curl -H "X-API-Key: tgp_test123456789" \
     http://localhost:3000/api/v1.0/proxy/bot123:test/getMe
```

## Security Best Practices

1. **Use HTTPS in Production**: Always deploy with SSL/TLS certificates
2. **Secure API Keys**: Store API keys securely, rotate regularly
3. **Monitor Usage**: Set up alerts for unusual usage patterns
4. **IP Restrictions**: Use IP allowlists for sensitive applications
5. **Rate Limiting**: Configure appropriate limits for your use case
6. **Regular Updates**: Keep dependencies and system updated

## Deployment

### Docker Deployment
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables
```bash
NODE_ENV=production
PORT=3000
MONGO_URI=mongodb://localhost:27017/telegram-proxy
REDIS_URI=redis://localhost:6379
```

### Load Balancing
The service is stateless and can be horizontally scaled behind a load balancer:
```nginx
upstream telegram_proxy {
    server proxy1:3000;
    server proxy2:3000;
    server proxy3:3000;
}
```

## Support

For issues, questions, or feature requests, please refer to the project documentation or contact the development team.

## License

This project is licensed under the MIT License.
