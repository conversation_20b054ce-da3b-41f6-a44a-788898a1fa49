# Telegram Proxy Service

A secure, scalable proxy service for the Telegram Bot API with authentication, rate limiting, webhook support, and comprehensive monitoring.

## 🚀 Features

### Core Features
- **🔐 API Key Authentication** - Secure access control with API key management
- **⚡ Rate Limiting** - Multi-level rate limiting (global IP + per API key)
- **🔗 Short URLs** - Clean, intuitive API endpoints (`/bot{TOKEN}/{METHOD}`)
- **📡 Webhook Support** - Complete webhook management and delivery
- **🛡️ Access Control** - IP/Domain/User blacklisting and whitelisting
- **📊 Monitoring & Alerting** - Real-time metrics and multi-channel alerts

### Advanced Features
- **📈 Prometheus Metrics** - Industry-standard metrics export
- **🔄 Retry Mechanisms** - Automatic retry with exponential backoff
- **🏥 Health Checks** - Comprehensive system health monitoring
- **📝 Usage Analytics** - Detailed usage statistics and reporting
- **🔧 Admin Tools** - Command-line tools for management
- **🌐 Horizontal Scaling** - Redis-based distributed architecture

## 📋 Quick Start

### Prerequisites
- Node.js 16+ 
- MongoDB 4.4+
- Redis 6.0+

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-org/telegram-proxy-service.git
cd telegram-proxy-service
```

2. **Install dependencies**
```bash
npm install
```

3. **Configure environment**
```bash
cp config/default.json.example config/default.json
# Edit config/default.json with your settings
```

4. **Start services**
```bash
# Start MongoDB and Redis
# Then start the proxy service
npm start
```

5. **Create your first API key**
```bash
# Create a user
node scripts/manage-api-keys.js create-<NAME_EMAIL> "My User"

# Create an API key
node scripts/manage-api-keys.js create-key USER_ID "My Bot Key"
```

## 🔧 Usage

### Basic API Calls

#### Short URL Format (Recommended)
```bash
# Get bot information
curl -H "X-API-Key: your_api_key" \
  https://your-proxy.com/bot123456:TOKEN/getMe

# Send message
curl -X POST -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"chat_id": "123456789", "text": "Hello World!"}' \
  https://your-proxy.com/bot123456:TOKEN/sendMessage

# Send photo
curl -X POST -H "X-API-Key: your_api_key" \
  -F "chat_id=123456789" \
  -F "photo=@image.jpg" \
  https://your-proxy.com/bot123456:TOKEN/sendPhoto
```

#### Legacy URL Format (Still Supported)
```bash
# Also works with legacy URLs
curl -H "X-API-Key: your_api_key" \
  https://your-proxy.com/api/v1.0/proxy/bot123456:TOKEN/getMe
```

### Webhook Management

```bash
# Setup webhook
curl -X POST -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "botToken": "123456:TOKEN",
    "targetUrl": "https://your-bot.com/webhook",
    "secretToken": "optional_secret"
  }' \
  https://your-proxy.com/api/v1.0/webhook/setup

# List webhooks
curl -H "X-API-Key: your_api_key" \
  https://your-proxy.com/api/v1.0/webhook/list

# Remove webhook
curl -X POST -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"botToken": "123456:TOKEN"}' \
  https://your-proxy.com/api/v1.0/webhook/remove
```

## 📊 Monitoring

### Health Check
```bash
curl https://your-proxy.com/health
```

### Metrics
```bash
# JSON metrics
curl https://your-proxy.com/metrics

# Prometheus format
curl https://your-proxy.com/metrics/prometheus
```

### Alerts
```bash
# Active alerts
curl https://your-proxy.com/alerts

# Alert history
curl https://your-proxy.com/alerts/history

# Alert statistics
curl https://your-proxy.com/alerts/stats
```

## 🛠️ Management

### API Key Management
```bash
# List all API keys
node scripts/manage-api-keys.js list

# Create new API key
node scripts/manage-api-keys.js create-key USER_ID "Key Name" \
  --daily-limit 5000 \
  --monthly-limit 150000

# Get usage statistics
node scripts/manage-api-keys.js stats API_KEY_ID

# Suspend API key
node scripts/manage-api-keys.js status API_KEY_ID suspended

# Delete API key
node scripts/manage-api-keys.js delete API_KEY_ID
```

### User Management
```bash
# Create user
node scripts/manage-api-keys.js create-<NAME_EMAIL> "Full Name"

# List users (via API keys)
node scripts/manage-api-keys.js list
```

## 🔒 Security Features

### Authentication
- **API Key Required** - All requests require valid API key
- **Multiple Auth Methods** - Header, query parameter, or Bearer token
- **Key Rotation** - Easy API key rotation and management

### Rate Limiting
- **Global Limits** - 100 requests/minute per IP address
- **API Key Limits** - 60 requests/minute per API key (configurable)
- **Graceful Handling** - Proper 429 responses with retry information

### Access Control
- **IP Blacklisting** - Block malicious IP addresses
- **IP Whitelisting** - Restrict access to specific IPs
- **User Blacklisting** - Block specific users
- **CIDR Support** - IP range blocking/allowing

## 📈 Performance

### Benchmarks
- **Health Endpoint**: <10ms response time
- **Proxy Requests**: <100ms overhead (excluding Telegram API time)
- **Concurrent Handling**: 1000+ concurrent requests
- **Memory Usage**: <100MB base usage

### Scaling
- **Horizontal Scaling** - Redis-based distributed rate limiting
- **Load Balancing** - Stateless design for easy load balancing
- **Database Optimization** - Indexed queries and connection pooling

## 📚 Documentation

- **[API Documentation](API_DOCUMENTATION.md)** - Complete API reference
- **[Migration Guide](MIGRATION_GUIDE.md)** - Migrating to short URLs
- **[Configuration Guide](docs/configuration.md)** - Detailed configuration options
- **[Deployment Guide](docs/deployment.md)** - Production deployment instructions

## 🧪 Testing

### Run Tests
```bash
# Test basic functionality
node test-proxy.js

# Test new features
node test-new-features.js

# Test short URLs
node test-short-urls.js

# Performance testing
node test-performance.js
```

## 🚀 Deployment

### Production Checklist
- [ ] Configure environment variables
- [ ] Set up MongoDB and Redis
- [ ] Configure SSL/TLS certificates
- [ ] Set up monitoring and alerting
- [ ] Configure backup strategies
- [ ] Set up log rotation
- [ ] Configure firewall rules

## 📊 Monitoring Integration

### Prometheus
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'telegram-proxy'
    static_configs:
      - targets: ['your-proxy.com:3009']
    metrics_path: '/metrics/prometheus'
```

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

- **Documentation**: Check the docs folder
- **Issues**: Create a GitHub issue
- **Health Check**: `GET /health`
- **Metrics**: `GET /metrics`

---

**Made with ❤️ for the Telegram Bot community**
