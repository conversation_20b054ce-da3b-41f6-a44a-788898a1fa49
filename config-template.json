{"redis": {"connections": {"master": {"host": "localhost", "port": 6379, "database": 0, "password": "your_redis_password"}}}, "mongo": {"connections": {"master": {"host": "localhost", "port": 27017, "database": "app-db", "options": {"useUnifiedTopology": true, "useNewUrlParser": true, "user": "your_mongo_user", "pass": "your_mongo_password"}}}}, "port": 3000, "logLevel": "info", "secretKey": "your_secret_key_for_jwt", "serviceName": "TELEGRAM-PROXY-SERVICE", "proxy": {"telegramApiUrl": "https://api.telegram.org", "telegramFileApiUrl": "https://api.telegram.org/file", "timeout": 30000, "maxRetries": 2, "retryDelay": 1000, "maxRequestSize": 52428800, "rateLimits": {"global": {"requestsPerMinute": 100, "blockDuration": 60}, "default": {"requestsPerMinute": 60, "requestsPerHour": 1000, "dailyLimit": 10000, "monthlyLimit": 300000}}}, "emailInfos": [{"service": "gmail", "auth": {"user": "<EMAIL>", "pass": "your_app_password"}}], "listEmailAlert": ["<EMAIL>"]}