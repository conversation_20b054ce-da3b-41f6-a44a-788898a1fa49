const express = require('express');
const cors = require('cors');

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Middleware
const bodyParser = require('body-parser');
const helmet = require('helmet');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');
const verifyTokenMiddleware = require('./lib/middleware/verifyToken');
const apiKeyAuthMiddleware = require('./lib/middleware/apiKeyAuth');
const rateLimiterMiddleware = require('./lib/middleware/rateLimiter');
const requestLoggerMiddleware = require('./lib/middleware/requestLogger');

// Handle routes
const ApiRoutes = require('./lib/routes/api');

// Start server
const app = express();
app.set('trust proxy', true);
const server = require('http').Server(app);
global.io = require('socket.io')(server);

// Middleware setup
app.use(helmet({
  contentSecurityPolicy: false, // Disable CSP for API
  crossOriginEmbedderPolicy: false
}));
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static('public'));

// Define route declaration function
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }

  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
};

// API Routes - Example routes for the template
declareRoute('post', '/auth/login', [], ApiRoutes.auth.login);
declareRoute('post', '/auth/register', [], ApiRoutes.auth.register);
declareRoute('post', '/user/profile', [tokenToUserMiddleware], ApiRoutes.user.profile);
declareRoute('post', '/user/update', [tokenToUserMiddleware], ApiRoutes.user.update);

// Telegram Proxy Routes
// Catch-all route for Telegram Bot API proxy
app.all('/api/v1.0/proxy/*', [
  rateLimiterMiddleware,
  apiKeyAuthMiddleware,
  requestLoggerMiddleware
], ApiRoutes.proxy['v1.0']);

// Alternative route pattern for direct bot API access
app.all('/api/v1.0/bot*', [
  rateLimiterMiddleware,
  apiKeyAuthMiddleware,
  requestLoggerMiddleware
], (req, res) => {
  // Rewrite the path to include /proxy prefix
  req.params[0] = req.path.replace('/api/v1.0', '');
  return ApiRoutes.proxy['v1.0'](req, res);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

const port = _.get(config, 'port', 3000);
server.listen(port, () => {
  logger.logInfo('Server listening at port:', port);
});

process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
});
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
