const express = require('express');
const cors = require('cors');

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Middleware
const bodyParser = require('body-parser');
const helmet = require('helmet');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');
const verifyTokenMiddleware = require('./lib/middleware/verifyToken');
const apiKeyAuthMiddleware = require('./lib/middleware/apiKeyAuth');
const rateLimiterMiddleware = require('./lib/middleware/rateLimiter');
const requestLoggerMiddleware = require('./lib/middleware/requestLogger');
const accessControlMiddleware = require('./lib/middleware/accessControl');

// Services
const metricsService = require('./lib/services/metricsService');
const alertingService = require('./lib/services/alertingService');

// Handle routes
const ApiRoutes = require('./lib/routes/api');

// Start server
const app = express();
app.set('trust proxy', true);
const server = require('http').Server(app);
global.io = require('socket.io')(server);

// Middleware setup
app.use(helmet({
  contentSecurityPolicy: false, // Disable CSP for API
  crossOriginEmbedderPolicy: false
}));
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static('public'));

// Define route declaration function
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }

  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
};

// API Routes - Example routes for the template
declareRoute('post', '/auth/login', [], ApiRoutes.auth.login);
declareRoute('post', '/auth/register', [], ApiRoutes.auth.register);
declareRoute('post', '/user/profile', [tokenToUserMiddleware], ApiRoutes.user.profile);
declareRoute('post', '/user/update', [tokenToUserMiddleware], ApiRoutes.user.update);

// Telegram Proxy Routes - Short URL Format Only
app.all('/bot*', [
  accessControlMiddleware,
  rateLimiterMiddleware,
  apiKeyAuthMiddleware,
  requestLoggerMiddleware
], (req, res) => {
  // Process the bot API request directly
  req.params[0] = req.path;
  return ApiRoutes.proxy['v1.0'](req, res);
});

// Webhook Management Routes
app.post('/webhook/setup', [apiKeyAuthMiddleware], ApiRoutes.webhook['v1.0'].setupWebhook);
app.post('/webhook/remove', [apiKeyAuthMiddleware], ApiRoutes.webhook['v1.0'].removeWebhook);
app.get('/webhook/info/:botToken', [apiKeyAuthMiddleware], ApiRoutes.webhook['v1.0'].getWebhookInfo);
app.get('/webhook/list', [apiKeyAuthMiddleware], ApiRoutes.webhook['v1.0'].listWebhooks);

// Webhook handler (no auth required - Telegram calls this)
app.post('/webhook/:webhookId', ApiRoutes.webhook['v1.0'].handleIncomingWebhook);

// Health check endpoint
app.get('/health', (req, res) => {
  const health = metricsService.getHealthStatus();
  res.json(health);
});

// Metrics endpoints
app.get('/metrics', (req, res) => {
  const metrics = metricsService.getMetrics();
  res.json(metrics);
});

app.get('/metrics/prometheus', (req, res) => {
  const prometheusMetrics = metricsService.getPrometheusMetrics();
  res.set('Content-Type', 'text/plain');
  res.send(prometheusMetrics);
});

// Alert endpoints (admin only - you might want to add admin auth)
app.get('/alerts', (req, res) => {
  const alerts = alertingService.getActiveAlerts();
  res.json({ alerts });
});

app.get('/alerts/history', (req, res) => {
  const limit = parseInt(req.query.limit) || 100;
  const history = alertingService.getAlertHistory(limit);
  res.json({ history });
});

app.get('/alerts/stats', (req, res) => {
  const stats = alertingService.getAlertStats();
  res.json(stats);
});

app.post('/alerts/test', (req, res) => {
  alertingService.testAlert();
  res.json({ message: 'Test alert triggered' });
});

const port = _.get(config, 'port', 3000);
server.listen(port, () => {
  logger.logInfo('Server listening at port:', port);
});

process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
});
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
