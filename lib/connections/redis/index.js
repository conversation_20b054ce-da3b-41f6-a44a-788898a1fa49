const _ = require('lodash')
const config = require('config')
const redis = require('redis')

let connections = {}

class RedisConnection {
  constructor(name, options) {
    this.connection = null
    this.options = options
    this.name = name

    this.init()
  }

  async init() {
    this.connection = redis.createClient(this.options)

    this.connection.on('connect', () => {
      logger.logInfo(`[REDIS-${this.name}] - CONNECTED`)
    })

    this.connection.on('error', (err) => {
      logger.logError(`[REDIS-${this.name}]`, err)
    })

    this.connection.on('ready', () => {
      logger.logInfo(`[REDIS-${this.name}] - READY`)
      setInterval(async () => {
        try {
          await this.connection.ping();
        } catch (err) {
          console.error('Redis keepalive error', err);
        }
      }, 30000);
    })

    // Connect to Redis
    try {
      await this.connection.connect()
      logger.logInfo(`[REDIS-${this.name}] - CONNECTION ESTABLISHED`)
    } catch (error) {
      logger.logError(`[REDIS-${this.name}] - CONNECTION FAILED`, error)
    }
  }

  getConnection() {
    return this.connection
  }
}

async function setUp() {
  const redisConfig = _.get(config, 'redis.connections', {})
  const promises = Object.keys(redisConfig).map(async (name) => {
    connections[name] = new RedisConnection(name, redisConfig[name])
    // Wait a bit for connection to establish
    await new Promise(resolve => setTimeout(resolve, 1000))
  })
  await Promise.all(promises)
}

// Initialize connections
setUp().catch(console.error)

module.exports = (name) => {
  return connections[name] || null
}
