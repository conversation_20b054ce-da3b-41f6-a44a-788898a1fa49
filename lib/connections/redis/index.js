const _ = require('lodash')
const config = require('config')
const redis = require('redis')

let connections = {}

class RedisConnection {
  constructor(name, options) {
    this.connection = null
    this.options = options
    this.name = name

    this.init()
  }

  async init() {
    // Convert old config format to new Redis client format
    const clientOptions = {
      socket: {
        host: this.options.host || 'localhost',
        port: this.options.port || 6379
      }
    };

    // Add password if provided
    if (this.options.password) {
      clientOptions.password = this.options.password;
    }

    // Add database selection
    if (this.options.database !== undefined) {
      clientOptions.database = this.options.database;
    }

    console.log(`[REDIS-${this.name}] Connecting to ${clientOptions.socket.host}:${clientOptions.socket.port} db:${clientOptions.database}`);

    this.connection = redis.createClient(clientOptions)

    this.connection.on('connect', () => {
      logger.logInfo(`[REDIS-${this.name}] - CONNECTED`)
    })

    this.connection.on('error', (err) => {
      console.error(`[REDIS-${this.name}] ERROR:`, err.message);
      logger.logError(`[REDIS-${this.name}]`, err)
    })

    this.connection.on('ready', () => {
      logger.logInfo(`[REDIS-${this.name}] - READY`)
      setInterval(async () => {
        try {
          await this.connection.ping();
        } catch (err) {
          console.error('Redis keepalive error', err);
        }
      }, 30000);
    })

    // Connect to Redis with timeout
    try {
      console.log(`[REDIS-${this.name}] Attempting connection...`);

      // Add connection timeout
      const connectPromise = this.connection.connect();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout after 10s')), 10000)
      );

      await Promise.race([connectPromise, timeoutPromise]);

      console.log(`[REDIS-${this.name}] - CONNECTION ESTABLISHED`);
      logger.logInfo(`[REDIS-${this.name}] - CONNECTION ESTABLISHED`)
    } catch (error) {
      console.error(`[REDIS-${this.name}] - CONNECTION FAILED:`, error.message);
      logger.logError(`[REDIS-${this.name}] - CONNECTION FAILED`, error)
    }
  }

  getConnection() {
    return this.connection
  }
}

async function setUp() {
  const redisConfig = _.get(config, 'redis.connections', {})
  const promises = Object.keys(redisConfig).map(async (name) => {
    connections[name] = new RedisConnection(name, redisConfig[name])
    // Wait a bit for connection to establish
    await new Promise(resolve => setTimeout(resolve, 1000))
  })
  await Promise.all(promises)
}

// Initialize connections
setUp().catch(console.error)

module.exports = (name) => {
  return connections[name] || null
}
