module.exports = {
  CODE: {
    SUCCESS: 200,
    FAIL: 300,
    <PERSON>ON<PERSON>_PARAMS: 400,
    ACCESS_DENIED: 403,
    SYSTEM_ERROR: 500,
    TOKEN_EXPIRE: 401,
    UNAUTHORIZED: 401,
    RATE_LIMIT_EXCEEDED: 429
  },
  USER_STATUS: {
    INACTIVE: 0,
    ACTIVE: 1
  },
  USER_ROLES: {
    USER: 'user',
    ADMIN: 'admin'
  },
  API_KEY_STATUS: {
    ACTIVE: 'active',
    SUSPENDED: 'suspended',
    REVOKED: 'revoked'
  },
  PROXY: {
    MAX_REQUEST_SIZE: 50 * 1024 * 1024, // 50MB
    DEFAULT_TIMEOUT: 30000, // 30 seconds
    MAX_RETRIES: 2,
    RETRY_DELAY: 1000 // 1 second
  }
}
