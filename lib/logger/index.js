const winston = require('winston')
const config = require('config')
const _ = require('lodash')
const fs = require('fs')

function setUp(dir) {
  fs.existsSync(dir) || fs.mkdirSync(dir)

  const logLevel = _.get(config, 'logLevel', 'info')

  const logger = winston.createLogger({
    levels: {
      error: 0,
      info: 1
    },
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    ),
    transports: [
      new winston.transports.Console({
        level: 'info',
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.printf((info) => {
            const { level, message, data, ...meta } = info;
            let output = `${level}: ${message}`;

            // Add data if present
            if (data && Array.isArray(data) && data.length > 0) {
              output += ` ${data.join(' ')}`;
            }

            // Add any other metadata
            const otherMeta = Object.entries(meta)
              .filter(([key]) => !['timestamp', 'level', 'message', 'data'].includes(key))
              .map(([key, value]) => `${key}=${value}`)
              .join(' ');
            if (otherMeta) {
              output += ` ${otherMeta}`;
            }

            return output;
          })
        )
      }),
      new (require('winston-daily-rotate-file'))({
        level: 'error',
        datePattern: 'DD-MM-YYYY',
        filename: dir + '/system-%DATE%.log',
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        )
      })
    ]
  })

  // Add colors
  winston.addColors({
    error: 'red',
    info: 'green'
  })

  const obj = {
    logInfo: () => {},
    logError: (...args) => {
      if (args.length === 1) {
        logger.error(args[0]);
      } else {
        const [message, ...rest] = args;
        logger.error(message, { data: rest });
      }
    }
  }

  if(logLevel === 'info') {
    obj.logInfo = (...args) => {
      if (args.length === 1) {
        logger.info(args[0]);
      } else {
        const [message, ...rest] = args;
        logger.info(message, { data: rest });
      }
    }
  }

  return obj
}

module.exports = setUp
