const winston = require('winston')
const config = require('config')
const _ = require('lodash')
const fs = require('fs')

function setUp(dir) {
  fs.existsSync(dir) || fs.mkdirSync(dir)

  const logLevel = _.get(config, 'logLevel', 'info')

  const logger = winston.createLogger({
    levels: {
      error: 0,
      info: 1
    },
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    ),
    transports: [
      new winston.transports.Console({
        level: 'info',
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.printf(({ level, message }) => {
            return `${level}: ${message}`;
          })
        )
      }),
      new (require('winston-daily-rotate-file'))({
        level: 'error',
        datePattern: 'DD-MM-YYYY',
        filename: dir + '/system-%DATE%.log',
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        )
      })
    ]
  })

  // Add colors
  winston.addColors({
    error: 'red',
    info: 'green'
  })

  const obj = {
    logInfo: () => {},
    logError: (...args) => {
      logger.error(...args, {})
    }
  }

  if(logLevel === 'info') {
    obj.logInfo = (...args) => {
      logger.info(...args, {})
    }
  }

  return obj
}

module.exports = setUp
