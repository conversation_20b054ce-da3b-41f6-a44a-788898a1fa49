module.exports = {
  USER: {
    CREATE_SUCCESS: {
      head: 'Success',
      body: 'User account created successfully.'
    },
    UPDATE_SUCCESS: {
      head: 'Success',
      body: 'User account updated successfully.'
    },
    EXISTS: {
      head: 'Error',
      body: 'User already exists.'
    },
    NOT_EXISTS: {
      head: 'Error',
      body: 'User does not exist.'
    },
    NOT_CHANGE: {
      head: 'Warning',
      body: 'No changes were made to the user account.'
    },
    INCORRECT_PASSWORD: {
      head: 'Error',
      body: 'Incorrect password. Please try again.'
    },
    TOKEN_EXPIRE: {
      head: 'Session Expired',
      body: 'Your session has expired. Please log in again.'
    }
  },
  SYSTEM: {
    ERROR: {
      head: 'System Error',
      body: 'An error occurred. Please try again later.'
    },
    WRONG_PARAMS: {
      head: 'Invalid Parameters',
      body: 'Please check your input and try again.'
    },
    SUCCESS: {
      head: 'Success',
      body: 'Operation completed successfully.'
    }
  },
  PROXY: {
    API_KEY_REQUIRED: {
      head: 'Authentication Required',
      body: 'API key is required to access the proxy service.'
    },
    INVALID_API_KEY: {
      head: 'Invalid API Key',
      body: 'The provided API key is invalid or not found.'
    },
    API_KEY_INACTIVE: {
      head: 'API Key Inactive',
      body: 'The API key is not active or has been suspended.'
    },
    USER_ACCOUNT_INACTIVE: {
      head: 'Account Inactive',
      body: 'The user account associated with this API key is inactive.'
    },
    IP_NOT_ALLOWED: {
      head: 'IP Not Allowed',
      body: 'Your IP address is not allowed to use this API key.'
    },
    RATE_LIMIT_EXCEEDED: {
      head: 'Rate Limit Exceeded',
      body: 'You have exceeded the rate limit for your API key.'
    },
    GLOBAL_RATE_LIMIT_EXCEEDED: {
      head: 'Global Rate Limit Exceeded',
      body: 'Too many requests from your IP address.'
    },
    API_KEY_RATE_LIMIT_EXCEEDED: {
      head: 'API Key Rate Limit Exceeded',
      body: 'You have exceeded the rate limit for your API key.'
    },
    METHOD_NOT_ALLOWED: {
      head: 'Method Not Allowed',
      body: 'The HTTP method is not supported for this endpoint.'
    },
    INVALID_PATH: {
      head: 'Invalid Path',
      body: 'The request path is not a valid Telegram API path. Use format: /bot{TOKEN}/{METHOD}'
    },
    INVALID_BOT_TOKEN: {
      head: 'Invalid Bot Token',
      body: 'The bot token in the path is invalid or missing.'
    },
    INVALID_CONTENT_TYPE: {
      head: 'Invalid Content Type',
      body: 'The content type is not supported for this request.'
    }
  },
  WEBHOOK: {
    SETUP_SUCCESS: {
      head: 'Webhook Setup Successful',
      body: 'Webhook has been configured successfully.'
    },
    REMOVE_SUCCESS: {
      head: 'Webhook Removed',
      body: 'Webhook has been removed successfully.'
    },
    NOT_FOUND: {
      head: 'Webhook Not Found',
      body: 'The requested webhook was not found.'
    },
    MISSING_REQUIRED_FIELDS: {
      head: 'Missing Required Fields',
      body: 'Bot token and target URL are required for webhook setup.'
    },
    MISSING_BOT_TOKEN: {
      head: 'Missing Bot Token',
      body: 'Bot token is required for this operation.'
    },
    INFO_SUCCESS: {
      head: 'Webhook Information',
      body: 'Webhook information retrieved successfully.'
    },
    LIST_SUCCESS: {
      head: 'Webhook List',
      body: 'Webhook list retrieved successfully.'
    }
  },
  ACCESS_CONTROL: {
    IP_BLOCKED: {
      head: 'IP Address Blocked',
      body: 'Your IP address has been blocked from accessing this service.'
    },
    USER_BLOCKED: {
      head: 'User Account Blocked',
      body: 'Your user account has been blocked from accessing this service.'
    },
    ACCESS_DENIED: {
      head: 'Access Denied',
      body: 'Access to this resource has been denied.'
    }
  },
  MONITORING: {
    METRICS_SUCCESS: {
      head: 'Metrics Retrieved',
      body: 'System metrics retrieved successfully.'
    },
    HEALTH_CHECK_SUCCESS: {
      head: 'Health Check',
      body: 'System health check completed successfully.'
    },
    ALERT_TRIGGERED: {
      head: 'Alert Triggered',
      body: 'A system alert has been triggered.'
    }
  }
}
