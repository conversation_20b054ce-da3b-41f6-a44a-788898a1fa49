const CONSTANTS = require('../const');
const MESSAGES = require('../message');

/**
 * Access Control Middleware
 * Checks IP blacklist/whitelist and user blacklist
 */
module.exports = async (req, res, next) => {
  try {
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    const apiKey = req.apiKey;
    const userId = apiKey ? apiKey.userId : null;
    const apiKeyId = apiKey ? apiKey.keyId : null;

    // Check IP access control
    const ipAccessResult = await AccessControlModel.checkIPAccess(clientIP, apiKeyId, userId);
    
    if (!ipAccessResult.allowed) {
      logger.logWarn('Access denied by IP access control', {
        clientIP,
        apiKeyId,
        userId,
        reason: ipAccessResult.reason,
        ruleId: ipAccessResult.rule ? ipAccessResult.rule.entryId : null
      });

      return res.status(403).json({
        code: CONSTANTS.CODE.ACCESS_DENIED,
        message: MESSAGES.ACCESS_CONTROL.IP_BLOCKED,
        error: `Access denied: ${ipAccessResult.reason}`
      });
    }

    // Check user access control (if user is authenticated)
    if (userId) {
      const userAccessResult = await AccessControlModel.checkUserAccess(userId);
      
      if (!userAccessResult.allowed) {
        logger.logWarn('Access denied by user access control', {
          clientIP,
          apiKeyId,
          userId,
          reason: userAccessResult.reason,
          ruleId: userAccessResult.rule ? userAccessResult.rule.entryId : null
        });

        return res.status(403).json({
          code: CONSTANTS.CODE.ACCESS_DENIED,
          message: MESSAGES.ACCESS_CONTROL.USER_BLOCKED,
          error: `Access denied: ${userAccessResult.reason}`
        });
      }
    }

    // Access granted, continue to next middleware
    next();

  } catch (error) {
    logger.logError('Access Control Middleware Error:', error);
    
    // On error, allow access but log the error
    // This ensures the service remains available even if access control fails
    next();
  }
};
