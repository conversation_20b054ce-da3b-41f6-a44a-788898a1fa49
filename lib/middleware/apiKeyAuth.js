const _ = require('lodash');
const CONSTANTS = require('../const');
const MESSAGES = require('../message');

/**
 * Middleware to authenticate API key and attach API key info to request
 */
module.exports = async (req, res, next) => {
  try {
    // Extract API key from various sources
    let apiKey = null;
    
    // Check X-API-Key header (preferred method)
    apiKey = _.get(req, 'headers.x-api-key', null);
    
    // Check Authorization header with <PERSON><PERSON> token
    if (!apiKey) {
      const authHeader = _.get(req, 'headers.authorization', '');
      if (authHeader.startsWith('Bearer ')) {
        apiKey = authHeader.substring(7);
      }
    }
    
    // Check query parameter (less secure, but supported)
    if (!apiKey) {
      apiKey = _.get(req, 'query.api_key', null);
    }
    
    // Check if API key is provided
    if (!apiKey) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: MESSAGES.PROXY.API_KEY_REQUIRED,
        error: 'API key is required. Provide it via X-API-Key header, Authorization Bearer token, or api_key query parameter.'
      });
    }
    
    // Validate API key format (basic validation)
    if (typeof apiKey !== 'string' || apiKey.length < 10) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: MESSAGES.PROXY.INVALID_API_KEY,
        error: 'Invalid API key format.'
      });
    }
    
    // Find API key in database
    const apiKeyDoc = await ApiKeyModel.findOne({ 
      keyId: apiKey 
    }).populate('userId', 'username email name status');
    
    if (!apiKeyDoc) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: MESSAGES.PROXY.INVALID_API_KEY,
        error: 'API key not found.'
      });
    }
    
    // Check if API key is active
    if (!apiKeyDoc.isActive()) {
      let errorMessage = 'API key is not active.';
      
      if (apiKeyDoc.status === 'suspended') {
        errorMessage = 'API key is suspended.';
      } else if (apiKeyDoc.status === 'revoked') {
        errorMessage = 'API key has been revoked.';
      } else if (apiKeyDoc.expiresAt && apiKeyDoc.expiresAt < new Date()) {
        errorMessage = 'API key has expired.';
      }
      
      return res.status(403).json({
        code: CONSTANTS.CODE.ACCESS_DENIED,
        message: MESSAGES.PROXY.API_KEY_INACTIVE,
        error: errorMessage
      });
    }
    
    // Check if user account is active
    if (!apiKeyDoc.userId || apiKeyDoc.userId.status !== 1) {
      return res.status(403).json({
        code: CONSTANTS.CODE.ACCESS_DENIED,
        message: MESSAGES.PROXY.USER_ACCOUNT_INACTIVE,
        error: 'User account is inactive.'
      });
    }
    
    // Check IP restrictions if configured
    if (apiKeyDoc.allowedIPs && apiKeyDoc.allowedIPs.length > 0) {
      const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
      const isAllowedIP = apiKeyDoc.allowedIPs.some(allowedIP => {
        // Support for CIDR notation could be added here
        return allowedIP === clientIP;
      });
      
      if (!isAllowedIP) {
        return res.status(403).json({
          code: CONSTANTS.CODE.ACCESS_DENIED,
          message: MESSAGES.PROXY.IP_NOT_ALLOWED,
          error: `IP address ${clientIP} is not allowed for this API key.`
        });
      }
    }
    
    // Check usage limits
    if (!apiKeyDoc.canMakeRequest()) {
      let errorMessage = 'Usage limit exceeded.';
      
      if (apiKeyDoc.dailyUsage >= apiKeyDoc.dailyLimit) {
        errorMessage = `Daily limit of ${apiKeyDoc.dailyLimit} requests exceeded.`;
      } else if (apiKeyDoc.monthlyUsage >= apiKeyDoc.monthlyLimit) {
        errorMessage = `Monthly limit of ${apiKeyDoc.monthlyLimit} requests exceeded.`;
      }
      
      return res.status(429).json({
        code: CONSTANTS.CODE.RATE_LIMIT_EXCEEDED,
        message: MESSAGES.PROXY.RATE_LIMIT_EXCEEDED,
        error: errorMessage,
        limits: {
          daily: {
            limit: apiKeyDoc.dailyLimit,
            used: apiKeyDoc.dailyUsage,
            remaining: Math.max(0, apiKeyDoc.dailyLimit - apiKeyDoc.dailyUsage)
          },
          monthly: {
            limit: apiKeyDoc.monthlyLimit,
            used: apiKeyDoc.monthlyUsage,
            remaining: Math.max(0, apiKeyDoc.monthlyLimit - apiKeyDoc.monthlyUsage)
          }
        }
      });
    }
    
    // Update last used information
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
    apiKeyDoc.lastUsedIP = clientIP;
    
    // Attach API key info to request for use in subsequent middleware
    req.apiKey = {
      id: apiKeyDoc._id,
      keyId: apiKeyDoc.keyId,
      userId: apiKeyDoc.userId._id,
      user: apiKeyDoc.userId,
      limits: {
        daily: apiKeyDoc.dailyLimit,
        monthly: apiKeyDoc.monthlyLimit,
        rateLimit: apiKeyDoc.rateLimit
      },
      usage: {
        daily: apiKeyDoc.dailyUsage,
        monthly: apiKeyDoc.monthlyUsage,
        total: apiKeyDoc.totalUsage
      },
      doc: apiKeyDoc // Full document for usage tracking
    };
    
    next();
    
  } catch (error) {
    logger.logError('API Key Authentication Error:', error);
    
    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
      error: 'Internal server error during authentication.'
    });
  }
};
