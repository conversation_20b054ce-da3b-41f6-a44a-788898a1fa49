const creditService = require('../services/creditService');
const CONSTANTS = require('../const');
const MESSAGES = require('../message');

// Import models (they should be loaded globally, but explicit import for safety)
const SystemConfigModel = global.SystemConfigModel || require('../models/systemConfig');

/**
 * Credit Check Middleware
 * Checks if user has enough credits and deducts credits for API requests
 */
module.exports = async (req, res, next) => {
  try {
    console.log('🔍 Credit middleware called for:', req.path);

    // Skip credit check if credit system is disabled
    const creditSystemEnabled = await SystemConfigModel.getCachedValue('credit_system_enabled', true);
    if (!creditSystemEnabled) {
      console.log('⚠️  Credit system disabled, skipping');
      return next();
    }

    // Get user ID from API key
    const apiKey = req.apiKey;
    if (!apiKey || !apiKey.userId) {
      return res.status(401).json({
        code: CONSTANTS.CODE.UNAUTHORIZED,
        message: MESSAGES.AUTH.INVALID_API_KEY,
        error: 'Valid API key required for credit system'
      });
    }

    const userId = apiKey.userId;
    const apiKeyId = apiKey.keyId;

    // Check if user has enough credits
    const creditCheck = await creditService.checkCredits(userId, 1);

    if (!creditCheck.hasEnough) {
      // Log insufficient credits
      if (global.logger && global.logger.logWarn) {
        global.logger.logWarn('Insufficient credits', {
        userId,
        apiKeyId,
        totalCredits: creditCheck.totalCredits,
        monthlyCredits: creditCheck.monthlyCredits,
        adCredits: creditCheck.adCredits,
        clientIP: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.path
        });
      } else {
        console.warn('Insufficient credits', {
          userId,
          apiKeyId,
          totalCredits: creditCheck.totalCredits,
          monthlyCredits: creditCheck.monthlyCredits,
          adCredits: creditCheck.adCredits,
          clientIP: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.path
        });
      }

      // Return 402 Payment Required
      return res.status(402).json({
        code: CONSTANTS.CODE.PAYMENT_REQUIRED,
        message: MESSAGES.CREDITS.INSUFFICIENT,
        error: 'Insufficient credits to process request',
        data: {
          currentCredits: creditCheck.totalCredits,
          monthlyCredits: creditCheck.monthlyCredits,
          adCredits: creditCheck.adCredits,
          required: creditCheck.required
        }
      });
    }

    // Store credit info in request for later deduction
    req.creditInfo = {
      userId,
      apiKeyId,
      hasEnoughCredits: true,
      creditsAvailable: creditCheck.totalCredits
    };

    // Add response interceptor to deduct credits after successful request
    const originalSend = res.send;
    const originalJson = res.json;

    let creditDeducted = false;

    const deductCreditsIfSuccess = async (statusCode) => {
      if (creditDeducted) return; // Prevent double deduction
      creditDeducted = true;

      console.log(`💳 Credit deduction check: status=${statusCode}, user=${userId}`);

      try {
        // Only deduct credits for successful requests (2xx status codes)
        if (statusCode >= 200 && statusCode < 300) {
          console.log('✅ Deducting 1 credit for successful request');
          const requestDetails = {
            method: req.method,
            endpoint: req.path,
            responseStatus: statusCode,
            responseTime: Date.now() - req.startTime,
            userAgent: req.get('User-Agent'),
            clientIP: req.ip
          };

          await creditService.deductCreditsForRequest(
            userId,
            apiKeyId,
            requestDetails,
            1
          );

          // Log successful credit deduction
          if (global.logger && global.logger.logInfo) {
            global.logger.logInfo('Credits deducted', {
            userId,
            apiKeyId,
            amount: 1,
            endpoint: req.path,
              responseStatus: statusCode
            });
          } else {
            console.log('Credits deducted', {
              userId,
              apiKeyId,
              amount: 1,
              endpoint: req.path,
              responseStatus: statusCode
            });
          }
        } else {
          console.log(`⚠️  Not deducting credits for non-2xx response: ${statusCode}`);
          // Log that credits were not deducted due to error response
          if (global.logger && global.logger.logInfo) {
            global.logger.logInfo('Credits not deducted due to error response', {
              userId,
              apiKeyId,
              endpoint: req.path,
              responseStatus: statusCode
            });
          } else {
            console.log('Credits not deducted due to error response', {
              userId,
              apiKeyId,
              endpoint: req.path,
              responseStatus: statusCode
            });
          }
        }
      } catch (error) {
        // Log credit deduction error but don't fail the request
        if (global.logger && global.logger.logError) {
          global.logger.logError('Credit deduction error:', {
            userId,
            apiKeyId,
            error: error.message,
            endpoint: req.path
          });
        } else {
          console.error('Credit deduction error:', {
            userId,
            apiKeyId,
            error: error.message,
            endpoint: req.path
          });
        }
      }
    };

    // Override res.send
    res.send = function(body) {
      deductCreditsIfSuccess(this.statusCode);
      return originalSend.call(this, body);
    };

    // Override res.json
    res.json = function(obj) {
      deductCreditsIfSuccess(this.statusCode);
      return originalJson.call(this, obj);
    };

    // Add credit headers to response
    res.set({
      'X-Credits-Available': creditCheck.totalCredits,
      'X-Credits-Monthly': creditCheck.monthlyCredits,
      'X-Credits-Ad': creditCheck.adCredits,
      'X-Credits-Required': creditCheck.required
    });

    next();

  } catch (error) {
    if (global.logger && global.logger.logError) {
      global.logger.logError('Credit Check Middleware Error:', error);
    } else {
      console.error('Credit Check Middleware Error:', error);
    }

    // On error, allow request to proceed but log the error
    // This ensures service availability even if credit system fails
    if (global.logger && global.logger.logWarn) {
      global.logger.logWarn('Credit check failed, allowing request to proceed', {
        error: error.message,
        userId: req.apiKey?.userId,
        apiKeyId: req.apiKey?.keyId,
        endpoint: req.path
      });
    } else {
      console.warn('Credit check failed, allowing request to proceed', {
        error: error.message,
        userId: req.apiKey?.userId,
        apiKeyId: req.apiKey?.keyId,
        endpoint: req.path
      });
    }

    next();
  }
};
