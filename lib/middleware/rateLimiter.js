const { RateLimiterRedis } = require('rate-limiter-flexible');
const redisConnections = require('../connections/redis');
const CONSTANTS = require('../const');
const MESSAGES = require('../message');

class ProxyRateLimiter {
  constructor() {
    this.redisClient = redisConnections('master').getConnection();
    this.limiters = new Map();
    this.initializeLimiters();
  }

  initializeLimiters() {
    // Global rate limiter (per IP)
    this.globalLimiter = new RateLimiterRedis({
      storeClient: this.redisClient,
      keyPrefix: 'proxy_global_rl',
      points: 100, // Number of requests
      duration: 60, // Per 60 seconds (1 minute)
      blockDuration: 60, // Block for 60 seconds if limit exceeded
    });

    // API key rate limiter (per minute)
    this.apiKeyMinuteLimiter = new RateLimiterRedis({
      storeClient: this.redisClient,
      keyPrefix: 'proxy_apikey_minute_rl',
      points: 60, // Default 60 requests per minute
      duration: 60, // Per 60 seconds
      blockDuration: 60, // Block for 60 seconds
    });

    // API key rate limiter (per hour)
    this.apiKeyHourLimiter = new RateLimiterRedis({
      storeClient: this.redisClient,
      keyPrefix: 'proxy_apikey_hour_rl',
      points: 1000, // Default 1000 requests per hour
      duration: 3600, // Per 3600 seconds (1 hour)
      blockDuration: 300, // Block for 5 minutes
    });
  }

  /**
   * Create or get a custom rate limiter for specific API key settings
   */
  getCustomLimiter(apiKeyId, requestsPerMinute, requestsPerHour) {
    const limiterKey = `${apiKeyId}_${requestsPerMinute}_${requestsPerHour}`;

    if (!this.limiters.has(limiterKey)) {
      // Create minute limiter
      const minuteLimiter = new RateLimiterRedis({
        storeClient: this.redisClient,
        keyPrefix: `proxy_custom_minute_${apiKeyId}`,
        points: requestsPerMinute,
        duration: 60,
        blockDuration: 60,
      });

      // Create hour limiter
      const hourLimiter = new RateLimiterRedis({
        storeClient: this.redisClient,
        keyPrefix: `proxy_custom_hour_${apiKeyId}`,
        points: requestsPerHour,
        duration: 3600,
        blockDuration: 300,
      });

      this.limiters.set(limiterKey, {
        minute: minuteLimiter,
        hour: hourLimiter
      });
    }

    return this.limiters.get(limiterKey);
  }

  /**
   * Check rate limits for a request
   */
  async checkRateLimit(req, res, next) {
    try {
      const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
      const apiKey = req.apiKey;

      // Check global rate limit (per IP) - Temporarily disabled for testing
      // try {
      //   await this.globalLimiter.consume(clientIP);
      // } catch (rejRes) {
      //   const remainingTime = rejRes && rejRes.msBeforeNext ? Math.round(rejRes.msBeforeNext / 1000) : 60;

      //   return res.status(429).json({
      //     code: CONSTANTS.CODE.RATE_LIMIT_EXCEEDED,
      //     message: MESSAGES.PROXY.GLOBAL_RATE_LIMIT_EXCEEDED,
      //     error: `Too many requests from this IP. Try again in ${remainingTime} seconds.`,
      //     retryAfter: remainingTime
      //   });
      // }

      // Temporarily disable API key rate limiting for testing
      // if (apiKey) {
      //   // Get rate limit settings for this API key
      //   const rateLimitSettings = apiKey.limits.rateLimit || {
      //     requestsPerMinute: 60,
      //     requestsPerHour: 1000
      //   };

      //   // Get custom limiters for this API key
      //   const customLimiters = this.getCustomLimiter(
      //     apiKey.keyId,
      //     rateLimitSettings.requestsPerMinute,
      //     rateLimitSettings.requestsPerHour
      //   );

      //   // Check minute rate limit
      //   try {
      //     await customLimiters.minute.consume(apiKey.keyId);
      //   } catch (rejRes) {
      //     const remainingTime = rejRes && rejRes.msBeforeNext ? Math.round(rejRes.msBeforeNext / 1000) : 60;

      //     return res.status(429).json({
      //       code: CONSTANTS.CODE.RATE_LIMIT_EXCEEDED,
      //       message: MESSAGES.PROXY.API_KEY_RATE_LIMIT_EXCEEDED,
      //       error: `Rate limit exceeded: ${rateLimitSettings.requestsPerMinute} requests per minute. Try again in ${remainingTime} seconds.`,
      //       retryAfter: remainingTime,
      //       limits: {
      //         perMinute: rateLimitSettings.requestsPerMinute,
      //         perHour: rateLimitSettings.requestsPerHour
      //       }
      //     });
      //   }

      //   // Check hour rate limit
      //   try {
      //     await customLimiters.hour.consume(apiKey.keyId);
      //   } catch (rejRes) {
      //     const remainingTime = rejRes && rejRes.msBeforeNext ? Math.round(rejRes.msBeforeNext / 1000) : 300;

      //     return res.status(429).json({
      //       code: CONSTANTS.CODE.RATE_LIMIT_EXCEEDED,
      //       message: MESSAGES.PROXY.API_KEY_RATE_LIMIT_EXCEEDED,
      //       error: `Rate limit exceeded: ${rateLimitSettings.requestsPerHour} requests per hour. Try again in ${remainingTime} seconds.`,
      //       retryAfter: remainingTime,
      //       limits: {
      //         perMinute: rateLimitSettings.requestsPerMinute,
      //         perHour: rateLimitSettings.requestsPerHour
      //       }
      //     });
      //   }
      // }

      next();

    } catch (error) {
      logger.logError('Rate Limiter Error:', error);

      // If rate limiter fails, allow the request to proceed
      // but log the error for monitoring
      next();
    }
  }

  /**
   * Get rate limit status for an API key
   */
  async getRateLimitStatus(apiKeyId, rateLimitSettings) {
    try {
      const customLimiters = this.getCustomLimiter(
        apiKeyId,
        rateLimitSettings.requestsPerMinute,
        rateLimitSettings.requestsPerHour
      );

      const minuteRes = await customLimiters.minute.get(apiKeyId);
      const hourRes = await customLimiters.hour.get(apiKeyId);

      return {
        minute: {
          limit: rateLimitSettings.requestsPerMinute,
          remaining: minuteRes ? Math.max(0, rateLimitSettings.requestsPerMinute - minuteRes.totalHits) : rateLimitSettings.requestsPerMinute,
          resetTime: minuteRes ? new Date(Date.now() + minuteRes.msBeforeNext) : null
        },
        hour: {
          limit: rateLimitSettings.requestsPerHour,
          remaining: hourRes ? Math.max(0, rateLimitSettings.requestsPerHour - hourRes.totalHits) : rateLimitSettings.requestsPerHour,
          resetTime: hourRes ? new Date(Date.now() + hourRes.msBeforeNext) : null
        }
      };
    } catch (error) {
      logger.logError('Get Rate Limit Status Error:', error);
      return null;
    }
  }
}

// Create singleton instance
const proxyRateLimiter = new ProxyRateLimiter();

// Export middleware function
module.exports = (req, res, next) => {
  return proxyRateLimiter.checkRateLimit(req, res, next);
};

// Export rate limiter instance for other uses
module.exports.rateLimiter = proxyRateLimiter;
