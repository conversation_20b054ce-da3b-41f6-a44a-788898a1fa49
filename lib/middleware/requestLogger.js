const crypto = require('crypto');
const _ = require('lodash');

/**
 * Middleware to log proxy requests (without sensitive content)
 */
module.exports = (req, res, next) => {
  // Generate unique request ID
  const requestId = crypto.randomUUID();
  req.requestId = requestId;

  // Capture request start time
  const startTime = Date.now();
  req.startTime = startTime;

  // Extract client information
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  const userAgent = req.get('User-Agent') || '';
  const contentType = req.get('Content-Type') || '';
  const contentLength = parseInt(req.get('Content-Length') || '0', 10);

  // Extract Telegram-specific information from path
  const telegramMethod = extractTelegramMethod(req.path);

  // Prepare base log data (no sensitive content)
  const baseLogData = {
    requestId,
    apiKeyId: req.apiKey ? req.apiKey.keyId : null,
    userId: req.apiKey ? req.apiKey.userId : null,
    method: req.method,
    endpoint: req.path,
    telegramMethod,
    userAgent,
    clientIP,
    contentType,
    contentLength,
    processingStartTime: new Date(startTime),
    timestamp: new Date(startTime)
  };

  // Store log data in request for later use
  req.logData = baseLogData;

  // Override res.json to capture response data
  const originalJson = res.json;
  const originalSend = res.send;
  const originalEnd = res.end;

  let responseLogged = false;

  const logResponse = async (responseData = null) => {
    if (responseLogged) return;
    responseLogged = true;

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    try {
      // Determine response size
      let responseSize = 0;
      if (responseData) {
        responseSize = Buffer.byteLength(typeof responseData === 'string' ? responseData : JSON.stringify(responseData), 'utf8');
      }

      // Determine success status
      const success = res.statusCode >= 200 && res.statusCode < 400;

      // Extract error information if applicable
      let errorType = null;
      let errorMessage = null;
      let telegramSuccess = null;
      let telegramErrorCode = null;
      let telegramErrorDescription = null;

      if (!success) {
        if (res.statusCode === 401) {
          errorType = 'auth_error';
        } else if (res.statusCode === 429) {
          errorType = 'rate_limit';
        } else if (res.statusCode >= 500) {
          errorType = 'proxy_error';
        } else if (res.statusCode >= 400) {
          errorType = 'validation_error';
        }

        // Extract error message from response (if it's JSON)
        if (responseData && typeof responseData === 'object') {
          const msgObj = responseData.message || responseData.error || 'Unknown error';
          errorMessage = typeof msgObj === 'string' ? msgObj : (msgObj.body || msgObj.head || JSON.stringify(msgObj));
        }
      }

      // Extract Telegram API response information
      if (responseData && typeof responseData === 'object') {
        if (responseData.hasOwnProperty('ok')) {
          telegramSuccess = responseData.ok;
          if (!responseData.ok) {
            telegramErrorCode = responseData.error_code;
            telegramErrorDescription = responseData.description;
            if (!errorType) errorType = 'telegram_error';
          }
        }
      }

      // Complete log data
      const completeLogData = {
        ...baseLogData,
        statusCode: res.statusCode,
        responseTime,
        responseSize,
        success,
        errorType,
        errorMessage,
        telegramSuccess,
        telegramErrorCode,
        telegramErrorDescription,
        processingEndTime: new Date(endTime)
      };

      // Save to database asynchronously (don't block response)
      setImmediate(async () => {
        try {
          await RequestLogModel.create(completeLogData);

          // Update API key usage if request was successful and API key exists
          if (success && req.apiKey && req.apiKey.doc) {
            await req.apiKey.doc.incrementUsage();
          }

        } catch (error) {
          logger.logError('Failed to save request log:', error);
        }
      });

      // Log to application logger (without sensitive data)
      const logLevel = success ? 'info' : 'warn';
      const logMessage = `${req.method} ${req.path} - ${res.statusCode} - ${responseTime}ms`;

      logger[logLevel === 'info' ? 'logInfo' : 'logError'](logMessage, {
        requestId,
        apiKeyId: req.apiKey ? req.apiKey.keyId : null,
        clientIP,
        statusCode: res.statusCode,
        responseTime,
        telegramMethod,
        success,
        errorType
      });

    } catch (error) {
      logger.logError('Request logging error:', error);
    }
  };

  // Override response methods
  res.json = function(data) {
    logResponse(data);
    return originalJson.call(this, data);
  };

  res.send = function(data) {
    logResponse(data);
    return originalSend.call(this, data);
  };

  res.end = function(data) {
    logResponse(data);
    return originalEnd.call(this, data);
  };

  // Handle cases where response is not sent through json/send/end
  res.on('finish', () => {
    logResponse();
  });

  next();
};

/**
 * Extract Telegram method from API path
 * @param {string} path - Request path
 * @returns {string|null} Telegram method name
 */
function extractTelegramMethod(path) {
  // Pattern: /bot{token}/{method}
  const match = path.match(/\/bot\d+:[A-Za-z0-9_-]+\/([A-Za-z0-9_]+)/);
  return match ? match[1] : null;
}
