const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema;

const AccessControlSchema = new mongoose.Schema(
  {
    // Entry identification
    entryId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    
    // Access control type
    type: {
      type: String,
      enum: ['ip_blacklist', 'ip_whitelist', 'domain_blacklist', 'domain_whitelist', 'user_blacklist'],
      required: true,
      index: true
    },
    
    // Target value (IP, domain, or user ID)
    value: {
      type: String,
      required: true,
      index: true
    },
    
    // CIDR notation for IP ranges (optional)
    cidr: {
      type: String
    },
    
    // Rule details
    description: {
      type: String,
      required: true
    },
    reason: {
      type: String,
      required: true
    },
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    },
    
    // Rule status
    status: {
      type: String,
      enum: ['active', 'inactive', 'expired'],
      default: 'active',
      index: true
    },
    
    // Scope and application
    scope: {
      type: String,
      enum: ['global', 'api_key', 'user'],
      default: 'global'
    },
    targetApiKeyId: {
      type: String,
      index: true // For API key specific rules
    },
    targetUserId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      index: true // For user specific rules
    },
    
    // Rule metadata
    createdBy: {
      type: String,
      required: true // Admin user ID or system
    },
    createdByType: {
      type: String,
      enum: ['admin', 'system', 'auto'],
      default: 'admin'
    },
    
    // Timing
    createdAt: {
      type: Date,
      default: Date.now,
      index: true
    },
    updatedAt: {
      type: Date,
      default: Date.now
    },
    expiresAt: {
      type: Date,
      index: true // For temporary blocks
    },
    
    // Statistics
    hitCount: {
      type: Number,
      default: 0
    },
    lastHitAt: {
      type: Date
    },
    
    // Additional metadata
    metadata: {
      type: Schema.Types.Mixed,
      default: {}
    },
    
    // Auto-detection info (for system-generated rules)
    autoDetected: {
      type: Boolean,
      default: false
    },
    detectionReason: {
      type: String
    },
    confidence: {
      type: Number,
      min: 0,
      max: 100
    }
  },
  { 
    id: false, 
    versionKey: false,
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
  }
);

// Indexes for performance
AccessControlSchema.index({ type: 1, status: 1 });
AccessControlSchema.index({ value: 1, type: 1 });
AccessControlSchema.index({ scope: 1, status: 1 });
AccessControlSchema.index({ expiresAt: 1 });
AccessControlSchema.index({ createdAt: 1 });

// TTL index for expired entries
AccessControlSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Methods
AccessControlSchema.methods.isActive = function() {
  if (this.status !== 'active') return false;
  if (this.expiresAt && this.expiresAt < new Date()) {
    this.status = 'expired';
    this.save();
    return false;
  }
  return true;
};

AccessControlSchema.methods.recordHit = function() {
  this.hitCount += 1;
  this.lastHitAt = new Date();
  return this.save();
};

AccessControlSchema.methods.deactivate = function() {
  this.status = 'inactive';
  this.updatedAt = new Date();
  return this.save();
};

// Static methods for checking access
AccessControlSchema.statics.checkIPAccess = async function(ip, apiKeyId = null, userId = null) {
  const queries = [
    // Global IP blacklist
    { type: 'ip_blacklist', value: ip, status: 'active', scope: 'global' },
    // Global IP whitelist (if exists, IP must be in whitelist)
    { type: 'ip_whitelist', status: 'active', scope: 'global' }
  ];

  // Add API key specific rules
  if (apiKeyId) {
    queries.push(
      { type: 'ip_blacklist', value: ip, status: 'active', scope: 'api_key', targetApiKeyId: apiKeyId },
      { type: 'ip_whitelist', status: 'active', scope: 'api_key', targetApiKeyId: apiKeyId }
    );
  }

  // Add user specific rules
  if (userId) {
    queries.push(
      { type: 'ip_blacklist', value: ip, status: 'active', scope: 'user', targetUserId: userId },
      { type: 'ip_whitelist', status: 'active', scope: 'user', targetUserId: userId }
    );
  }

  const rules = await this.find({ $or: queries });

  // Check blacklist first
  const blacklistRule = rules.find(rule => 
    rule.type === 'ip_blacklist' && 
    (rule.value === ip || this.matchesCIDR(ip, rule.cidr))
  );

  if (blacklistRule) {
    await blacklistRule.recordHit();
    return {
      allowed: false,
      reason: 'IP blacklisted',
      rule: blacklistRule
    };
  }

  // Check whitelist
  const whitelistRules = rules.filter(rule => rule.type === 'ip_whitelist');
  if (whitelistRules.length > 0) {
    const whitelistMatch = whitelistRules.find(rule => 
      rule.value === ip || this.matchesCIDR(ip, rule.cidr)
    );

    if (!whitelistMatch) {
      return {
        allowed: false,
        reason: 'IP not in whitelist',
        rule: null
      };
    }

    await whitelistMatch.recordHit();
  }

  return {
    allowed: true,
    reason: 'Access granted',
    rule: null
  };
};

AccessControlSchema.statics.checkUserAccess = async function(userId) {
  const blacklistRule = await this.findOne({
    type: 'user_blacklist',
    value: userId.toString(),
    status: 'active'
  });

  if (blacklistRule) {
    await blacklistRule.recordHit();
    return {
      allowed: false,
      reason: 'User blacklisted',
      rule: blacklistRule
    };
  }

  return {
    allowed: true,
    reason: 'Access granted',
    rule: null
  };
};

AccessControlSchema.statics.matchesCIDR = function(ip, cidr) {
  if (!cidr) return false;
  
  try {
    // Simple CIDR matching (you might want to use a library like 'ip-range-check')
    const [network, prefixLength] = cidr.split('/');
    if (!prefixLength) return ip === network;
    
    // This is a simplified implementation
    // In production, use a proper CIDR matching library
    return ip.startsWith(network.split('.').slice(0, Math.floor(parseInt(prefixLength) / 8)).join('.'));
  } catch {
    return false;
  }
};

// Static methods for management
AccessControlSchema.statics.addBlacklistEntry = async function(type, value, options = {}) {
  const {
    description = 'Blacklisted entry',
    reason = 'Security violation',
    severity = 'medium',
    scope = 'global',
    targetApiKeyId = null,
    targetUserId = null,
    createdBy = 'system',
    expiresAt = null,
    cidr = null
  } = options;

  const entryId = crypto.randomUUID();

  const entry = new this({
    entryId,
    type,
    value,
    cidr,
    description,
    reason,
    severity,
    scope,
    targetApiKeyId,
    targetUserId,
    createdBy,
    createdByType: createdBy === 'system' ? 'system' : 'admin',
    expiresAt
  });

  return await entry.save();
};

AccessControlSchema.statics.addWhitelistEntry = async function(type, value, options = {}) {
  const whitelistType = type.replace('blacklist', 'whitelist');
  return await this.addBlacklistEntry(whitelistType, value, options);
};

AccessControlSchema.statics.removeEntry = async function(entryId) {
  const entry = await this.findOne({ entryId });
  if (entry) {
    await entry.deactivate();
    return entry;
  }
  return null;
};

AccessControlSchema.statics.getActiveRules = async function(type = null, scope = null) {
  const query = { status: 'active' };
  if (type) query.type = type;
  if (scope) query.scope = scope;
  
  return await this.find(query).sort({ createdAt: -1 });
};

AccessControlSchema.statics.cleanupExpired = async function() {
  const result = await this.updateMany(
    { expiresAt: { $lt: new Date() }, status: 'active' },
    { status: 'expired' }
  );
  
  return result.modifiedCount;
};

module.exports = mongoConnections("master").model("AccessControl", AccessControlSchema);
