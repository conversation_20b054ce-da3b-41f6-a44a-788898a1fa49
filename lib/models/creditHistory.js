const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema;

const CreditHistorySchema = new mongoose.Schema(
  {
    // Transaction identification
    transactionId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },

    // User reference
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },

    // Transaction details
    type: {
      type: String,
      enum: ['add', 'subtract'],
      required: true,
      index: true
    },

    source: {
      type: String,
      enum: [
        'monthly_bonus',    // Monthly credit allocation
        'video_ad',         // Video ad completion
        'offerwall',        // Offerwall completion
        'usage',            // API request usage
        'admin_adjustment', // Manual admin adjustment
        'bonus',            // Special bonus
        'refund'            // Refund
      ],
      required: true,
      index: true
    },

    // Credit details
    amount: {
      type: Number,
      required: true,
      min: 0
    },

    creditType: {
      type: String,
      enum: ['monthly', 'ad', 'total'],
      required: true
    },

    // Balances before and after transaction
    balanceBefore: {
      total: { type: Number, default: 0 },
      monthly: { type: Number, default: 0 },
      ad: { type: Number, default: 0 }
    },

    balanceAfter: {
      total: { type: Number, default: 0 },
      monthly: { type: Number, default: 0 },
      ad: { type: Number, default: 0 }
    },

    // External references
    externalTransactionId: {
      type: String,
      index: true // For ad network callbacks
    },

    adNetworkId: {
      type: String,
      index: true // Which ad network provided the reward
    },

    apiKeyId: {
      type: String,
      index: true // Which API key was used (for usage tracking)
    },

    // Request details (for usage transactions)
    requestDetails: {
      method: String,
      endpoint: String,
      responseStatus: Number,
      responseTime: Number
    },

    // Additional metadata
    metadata: {
      type: Schema.Types.Mixed,
      default: {}
    },

    // Admin details (for manual adjustments)
    adminUserId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },

    adminReason: {
      type: String
    },

    // Timestamps
    createdAt: {
      type: Date,
      default: Date.now,
      index: true
    },

    // Processing status
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'cancelled'],
      default: 'completed',
      index: true
    },

    // Error details (if failed)
    errorMessage: {
      type: String
    }
  },
  {
    id: false,
    versionKey: false,
    timestamps: { createdAt: 'createdAt', updatedAt: false }
  }
);

// Indexes for performance
CreditHistorySchema.index({ userId: 1, createdAt: -1 });
CreditHistorySchema.index({ userId: 1, source: 1, createdAt: -1 });
CreditHistorySchema.index({ userId: 1, type: 1, createdAt: -1 });
CreditHistorySchema.index({ externalTransactionId: 1 });
CreditHistorySchema.index({ createdAt: 1 }); // For cleanup jobs

// Methods
CreditHistorySchema.methods.isCredit = function() {
  return this.type === 'add';
};

CreditHistorySchema.methods.isDebit = function() {
  return this.type === 'subtract';
};

CreditHistorySchema.methods.getNetAmount = function() {
  return this.type === 'add' ? this.amount : -this.amount;
};

// Static methods
CreditHistorySchema.statics.getUserHistory = function(userId, options = {}) {
  const {
    limit = 100,
    offset = 0,
    source = null,
    type = null,
    startDate = null,
    endDate = null
  } = options;

  const query = { userId };

  if (source) query.source = source;
  if (type) query.type = type;

  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) query.createdAt.$gte = new Date(startDate);
    if (endDate) query.createdAt.$lte = new Date(endDate);
  }

  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(offset)
    .lean();
};

CreditHistorySchema.statics.getUserSummary = function(userId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        userId: mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : userId,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          source: '$source',
          type: '$type'
        },
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: '$_id.source',
        transactions: {
          $push: {
            type: '$_id.type',
            amount: '$totalAmount',
            count: '$count'
          }
        },
        totalAmount: { $sum: '$totalAmount' }
      }
    }
  ]);
};

CreditHistorySchema.statics.getSystemStats = function(days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          source: '$source',
          type: '$type',
          date: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          }
        },
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.date': -1 }
    }
  ]);
};

CreditHistorySchema.statics.recordTransaction = async function(transactionData) {
  const {
    userId,
    type,
    source,
    amount,
    creditType,
    balanceBefore,
    balanceAfter,
    externalTransactionId = null,
    adNetworkId = null,
    apiKeyId = null,
    requestDetails = null,
    metadata = {},
    adminUserId = null,
    adminReason = null
  } = transactionData;

  const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const transaction = new this({
    transactionId,
    userId,
    type,
    source,
    amount,
    creditType,
    balanceBefore,
    balanceAfter,
    externalTransactionId,
    adNetworkId,
    apiKeyId,
    requestDetails,
    metadata,
    adminUserId,
    adminReason
  });

  return await transaction.save();
};

CreditHistorySchema.statics.getMonthlyUsage = function(userId, year, month) {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0, 23, 59, 59, 999);

  return this.aggregate([
    {
      $match: {
        userId: mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : userId,
        source: 'usage',
        createdAt: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: null,
        totalRequests: { $sum: '$amount' },
        totalTransactions: { $sum: 1 }
      }
    }
  ]);
};

CreditHistorySchema.statics.getDailyAdViews = function(userId, date = new Date()) {
  const startDate = new Date(date);
  startDate.setHours(0, 0, 0, 0);

  const endDate = new Date(date);
  endDate.setHours(23, 59, 59, 999);

  return this.countDocuments({
    userId,
    source: 'video_ad',
    type: 'add',
    createdAt: { $gte: startDate, $lte: endDate }
  });
};

CreditHistorySchema.statics.cleanupOldRecords = async function(daysToKeep = 365) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  const result = await this.deleteMany({
    createdAt: { $lt: cutoffDate },
    source: { $ne: 'monthly_bonus' } // Keep monthly bonus records longer
  });

  return result.deletedCount;
};

module.exports = mongoConnections("master").model("CreditHistory", CreditHistorySchema);
