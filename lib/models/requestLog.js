const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema;

const RequestLogSchema = new mongoose.Schema(
  {
    // Request identification
    requestId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    apiKeyId: {
      type: String,
      required: true,
      index: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    
    // Request details (sanitized - no sensitive content)
    method: {
      type: String,
      required: true,
      enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
    },
    endpoint: {
      type: String,
      required: true
    },
    telegramMethod: {
      type: String // e.g., 'sendMessage', 'getMe', etc.
    },
    
    // Request metadata
    userAgent: {
      type: String
    },
    clientIP: {
      type: String,
      required: true
    },
    contentType: {
      type: String
    },
    contentLength: {
      type: Number
    },
    
    // Response details
    statusCode: {
      type: Number,
      required: true
    },
    responseTime: {
      type: Number, // in milliseconds
      required: true
    },
    responseSize: {
      type: Number // in bytes
    },
    
    // Success/Error tracking
    success: {
      type: Boolean,
      required: true
    },
    errorType: {
      type: String,
      enum: ['auth_error', 'rate_limit', 'telegram_error', 'proxy_error', 'validation_error']
    },
    errorMessage: {
      type: String
    },
    
    // Telegram API response status
    telegramSuccess: {
      type: Boolean
    },
    telegramErrorCode: {
      type: Number
    },
    telegramErrorDescription: {
      type: String
    },
    
    // Timing information
    timestamp: {
      type: Date,
      default: Date.now,
      index: true
    },
    processingStartTime: {
      type: Date
    },
    processingEndTime: {
      type: Date
    },
    
    // Geographic information (optional)
    country: {
      type: String
    },
    region: {
      type: String
    },
    
    // Additional metadata
    metadata: {
      type: Schema.Types.Mixed,
      default: {}
    }
  },
  { 
    id: false, 
    versionKey: false 
  }
);

// Indexes for performance and queries
RequestLogSchema.index({ apiKeyId: 1, timestamp: -1 });
RequestLogSchema.index({ userId: 1, timestamp: -1 });
RequestLogSchema.index({ timestamp: -1 });
RequestLogSchema.index({ success: 1, timestamp: -1 });
RequestLogSchema.index({ errorType: 1, timestamp: -1 });
RequestLogSchema.index({ clientIP: 1, timestamp: -1 });

// TTL index to automatically delete old logs (optional - keep logs for 90 days)
RequestLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 90 * 24 * 60 * 60 });

// Static methods for analytics
RequestLogSchema.statics.getUsageStats = function(apiKeyId, startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        apiKeyId: apiKeyId,
        timestamp: {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $group: {
        _id: null,
        totalRequests: { $sum: 1 },
        successfulRequests: {
          $sum: { $cond: [{ $eq: ['$success', true] }, 1, 0] }
        },
        failedRequests: {
          $sum: { $cond: [{ $eq: ['$success', false] }, 1, 0] }
        },
        avgResponseTime: { $avg: '$responseTime' },
        totalDataTransferred: { $sum: '$responseSize' }
      }
    }
  ]);
};

RequestLogSchema.statics.getErrorStats = function(apiKeyId, startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        apiKeyId: apiKeyId,
        success: false,
        timestamp: {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $group: {
        _id: '$errorType',
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

module.exports = mongoConnections("master").model("RequestLog", RequestLogSchema);
