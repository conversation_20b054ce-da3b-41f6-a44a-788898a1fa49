const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const UserSchema = new mongoose.Schema(
 {
  username: {
   type: String,
   required: true,
   unique: true
  },
  password: {
   type: String,
   required: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  name: {
   type: String,
   required: true
  },
  phone: {
   type: String
  },
  avatar: {
    type: String
  },
  role: {
    type: String,
    default: 'user',
    enum: ['user', 'admin']
  },
  status: {
    type: Number,
    default: 1 // 1: active, 0: inactive
  },

  // Credit Management System
  totalCreditBalance: {
    type: Number,
    default: 0,
    min: 0
  },
  monthlyCreditBalance: {
    type: Number,
    default: 0,
    min: 0
  },
  adCreditBalance: {
    type: Number,
    default: 0,
    min: 0
  },

  // Credit tracking
  lastMonthlyCreditResetDate: {
    type: Date,
    default: null
  },

  // Ad viewing limits
  dailyAdViewsCount: {
    type: Number,
    default: 0,
    min: 0
  },
  lastAdViewDate: {
    type: Date,
    default: null
  },

  // Credit statistics
  totalCreditsEarned: {
    type: Number,
    default: 0,
    min: 0
  },
  totalCreditsUsed: {
    type: Number,
    default: 0,
    min: 0
  },

  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now }
 },
 { id: false, versionKey: false },
)

// Credit management methods
UserSchema.methods.getTotalCredits = function() {
  return this.monthlyCreditBalance + this.adCreditBalance;
};

UserSchema.methods.hasEnoughCredits = function(amount = 1) {
  return this.getTotalCredits() >= amount;
};

UserSchema.methods.deductCredits = function(amount = 1) {
  const totalAvailable = this.getTotalCredits();
  if (totalAvailable < amount) {
    throw new Error('Insufficient credits');
  }

  let remaining = amount;
  const deducted = { monthly: 0, ad: 0 };

  // Deduct from monthly credits first
  if (remaining > 0 && this.monthlyCreditBalance > 0) {
    const monthlyDeduction = Math.min(remaining, this.monthlyCreditBalance);
    this.monthlyCreditBalance -= monthlyDeduction;
    deducted.monthly = monthlyDeduction;
    remaining -= monthlyDeduction;
  }

  // Then deduct from ad credits
  if (remaining > 0 && this.adCreditBalance > 0) {
    const adDeduction = Math.min(remaining, this.adCreditBalance);
    this.adCreditBalance -= adDeduction;
    deducted.ad = adDeduction;
    remaining -= adDeduction;
  }

  // Update total credits used
  this.totalCreditsUsed += amount;
  this.updatedAt = Date.now();

  return deducted;
};

UserSchema.methods.addCredits = function(amount, type = 'ad') {
  if (type === 'monthly') {
    this.monthlyCreditBalance += amount;
  } else if (type === 'ad') {
    this.adCreditBalance += amount;
  }

  this.totalCreditsEarned += amount;
  this.updatedAt = Date.now();

  return this;
};

UserSchema.methods.resetMonthlyCredits = function(amount) {
  this.monthlyCreditBalance = amount;
  this.lastMonthlyCreditResetDate = new Date();
  this.updatedAt = Date.now();

  return this;
};

UserSchema.methods.canViewAd = function(dailyLimit = 10) {
  const today = new Date();
  const lastAdDate = this.lastAdViewDate;

  // Check if it's a new day
  if (!lastAdDate ||
      lastAdDate.getDate() !== today.getDate() ||
      lastAdDate.getMonth() !== today.getMonth() ||
      lastAdDate.getFullYear() !== today.getFullYear()) {
    return true; // New day, reset counter
  }

  return this.dailyAdViewsCount < dailyLimit;
};

UserSchema.methods.recordAdView = function() {
  const today = new Date();
  const lastAdDate = this.lastAdViewDate;

  // Reset counter if it's a new day
  if (!lastAdDate ||
      lastAdDate.getDate() !== today.getDate() ||
      lastAdDate.getMonth() !== today.getMonth() ||
      lastAdDate.getFullYear() !== today.getFullYear()) {
    this.dailyAdViewsCount = 1;
  } else {
    this.dailyAdViewsCount += 1;
  }

  this.lastAdViewDate = today;
  this.updatedAt = Date.now();

  return this;
};

UserSchema.methods.getCreditSummary = function() {
  return {
    total: this.getTotalCredits(),
    monthly: this.monthlyCreditBalance,
    ad: this.adCreditBalance,
    totalEarned: this.totalCreditsEarned,
    totalUsed: this.totalCreditsUsed,
    dailyAdViews: this.dailyAdViewsCount,
    lastAdViewDate: this.lastAdViewDate,
    lastMonthlyCreditReset: this.lastMonthlyCreditResetDate
  };
};

// Static methods
UserSchema.statics.getUsersForMonthlyReset = function() {
  return this.find({
    status: 1, // Active users only
    $or: [
      { lastMonthlyCreditResetDate: null },
      { lastMonthlyCreditResetDate: { $lt: new Date(new Date().getFullYear(), new Date().getMonth(), 1) } }
    ]
  });
};

UserSchema.statics.resetAllMonthlyCredits = async function(amount = 100000) {
  const users = await this.getUsersForMonthlyReset();
  const results = [];

  for (const user of users) {
    const oldBalance = user.monthlyCreditBalance;
    user.resetMonthlyCredits(amount);
    await user.save();

    results.push({
      userId: user._id,
      username: user.username,
      oldBalance,
      newBalance: user.monthlyCreditBalance
    });
  }

  return results;
};

module.exports = mongoConnections("master").model("User", UserSchema)
