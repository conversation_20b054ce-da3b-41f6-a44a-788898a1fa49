const mongoConnections = require("../connections/mongo");
const Schema = mongoose.Schema;

const WebhookSchema = new mongoose.Schema(
  {
    // Webhook identification
    webhookId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    apiKeyId: {
      type: String,
      required: true,
      index: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    botToken: {
      type: String,
      required: true,
      index: true
    },
    
    // Webhook configuration
    targetUrl: {
      type: String,
      required: true // User's webhook endpoint
    },
    proxyUrl: {
      type: String,
      required: true // Our proxy webhook URL
    },
    secretToken: {
      type: String // Optional secret token for verification
    },
    
    // Webhook settings
    maxConnections: {
      type: Number,
      default: 40,
      min: 1,
      max: 100
    },
    allowedUpdates: [{
      type: String // Array of allowed update types
    }],
    dropPendingUpdates: {
      type: Boolean,
      default: false
    },
    
    // Status and health
    status: {
      type: String,
      enum: ['active', 'inactive', 'error', 'suspended'],
      default: 'inactive'
    },
    lastError: {
      type: String
    },
    lastErrorTime: {
      type: Date
    },
    
    // Statistics
    totalUpdates: {
      type: Number,
      default: 0
    },
    successfulDeliveries: {
      type: Number,
      default: 0
    },
    failedDeliveries: {
      type: Number,
      default: 0
    },
    lastUpdateTime: {
      type: Date
    },
    
    // Health monitoring
    consecutiveFailures: {
      type: Number,
      default: 0
    },
    lastHealthCheck: {
      type: Date
    },
    healthStatus: {
      type: String,
      enum: ['healthy', 'warning', 'critical'],
      default: 'healthy'
    },
    
    // Metadata
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    },
    activatedAt: {
      type: Date
    },
    deactivatedAt: {
      type: Date
    }
  },
  { 
    id: false, 
    versionKey: false,
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
  }
);

// Indexes for performance
WebhookSchema.index({ apiKeyId: 1 });
WebhookSchema.index({ userId: 1 });
WebhookSchema.index({ botToken: 1 });
WebhookSchema.index({ status: 1 });
WebhookSchema.index({ createdAt: 1 });

// Methods
WebhookSchema.methods.isActive = function() {
  return this.status === 'active';
};

WebhookSchema.methods.recordSuccess = function() {
  this.successfulDeliveries += 1;
  this.totalUpdates += 1;
  this.consecutiveFailures = 0;
  this.lastUpdateTime = new Date();
  this.healthStatus = 'healthy';
  return this.save();
};

WebhookSchema.methods.recordFailure = function(error) {
  this.failedDeliveries += 1;
  this.totalUpdates += 1;
  this.consecutiveFailures += 1;
  this.lastError = error;
  this.lastErrorTime = new Date();
  
  // Update health status based on consecutive failures
  if (this.consecutiveFailures >= 10) {
    this.healthStatus = 'critical';
    this.status = 'error';
  } else if (this.consecutiveFailures >= 5) {
    this.healthStatus = 'warning';
  }
  
  return this.save();
};

WebhookSchema.methods.activate = function() {
  this.status = 'active';
  this.activatedAt = new Date();
  this.consecutiveFailures = 0;
  this.healthStatus = 'healthy';
  return this.save();
};

WebhookSchema.methods.deactivate = function() {
  this.status = 'inactive';
  this.deactivatedAt = new Date();
  return this.save();
};

WebhookSchema.methods.getSuccessRate = function() {
  if (this.totalUpdates === 0) return 100;
  return (this.successfulDeliveries / this.totalUpdates) * 100;
};

// Static methods
WebhookSchema.statics.findByBotToken = function(botToken) {
  return this.findOne({ botToken, status: 'active' });
};

WebhookSchema.statics.findByApiKey = function(apiKeyId) {
  return this.find({ apiKeyId }).populate('userId', 'username email');
};

WebhookSchema.statics.getHealthyWebhooks = function() {
  return this.find({ 
    status: 'active',
    healthStatus: { $in: ['healthy', 'warning'] }
  });
};

WebhookSchema.statics.getCriticalWebhooks = function() {
  return this.find({ 
    $or: [
      { status: 'error' },
      { healthStatus: 'critical' }
    ]
  }).populate('userId', 'username email');
};

module.exports = mongoConnections("master").model("Webhook", WebhookSchema);
