const crypto = require('crypto');
const creditService = require('../../../services/creditService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * Ad Network Callback Endpoints
 * These endpoints receive callbacks from ad networks when users complete tasks
 */

/**
 * Generic video ad callback
 * POST /callbacks/video-ad/:networkId
 */
async function videoAdCallback(req, res) {
  try {
    const { networkId } = req.params;
    const callbackData = req.body;

    logger.logInfo('Video ad callback received', {
      networkId,
      callbackData: { ...callbackData, signature: '[REDACTED]' },
      headers: req.headers,
      ip: req.ip
    });

    // Verify callback authenticity based on network
    const isValid = await verifyCallback(networkId, callbackData, req);
    if (!isValid) {
      logger.logWarn('Invalid video ad callback', { networkId, ip: req.ip });
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_CALLBACK,
        message: MESSAGES.CALLBACKS.INVALID_SIGNATURE,
        error: 'Invalid callback signature'
      });
    }

    // Extract user ID and transaction ID from callback
    const { userId, transactionId } = extractCallbackData(networkId, callbackData);
    
    if (!userId || !transactionId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.CALLBACKS.MISSING_DATA,
        error: 'Missing userId or transactionId in callback'
      });
    }

    // Process video ad completion
    const result = await creditService.processVideoAdCompletion(
      userId,
      networkId,
      transactionId
    );

    logger.logInfo('Video ad completion processed', {
      userId,
      networkId,
      transactionId,
      creditsEarned: result.creditsEarned
    });

    return res.status(200).json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.CALLBACKS.VIDEO_AD_SUCCESS,
      data: {
        processed: true,
        creditsEarned: result.creditsEarned,
        transactionId: result.transactionId
      }
    });

  } catch (error) {
    logger.logError('Video Ad Callback Error:', error);

    if (error.message.includes('limit exceeded')) {
      return res.status(200).json({
        code: CONSTANTS.CODE.RATE_LIMIT_EXCEEDED,
        message: MESSAGES.CALLBACKS.DAILY_LIMIT_EXCEEDED,
        data: { processed: false, reason: 'daily_limit_exceeded' }
      });
    }

    if (error.message.includes('already processed')) {
      return res.status(200).json({
        code: CONSTANTS.CODE.SUCCESS,
        message: MESSAGES.CALLBACKS.ALREADY_PROCESSED,
        data: { processed: false, reason: 'already_processed' }
      });
    }

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
      error: 'Internal server error'
    });
  }
}

/**
 * Generic offerwall callback
 * POST /callbacks/offerwall/:networkId
 */
async function offerwallCallback(req, res) {
  try {
    const { networkId } = req.params;
    const callbackData = req.body;

    logger.logInfo('Offerwall callback received', {
      networkId,
      callbackData: { ...callbackData, signature: '[REDACTED]' },
      headers: req.headers,
      ip: req.ip
    });

    // Verify callback authenticity
    const isValid = await verifyCallback(networkId, callbackData, req);
    if (!isValid) {
      logger.logWarn('Invalid offerwall callback', { networkId, ip: req.ip });
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_CALLBACK,
        message: MESSAGES.CALLBACKS.INVALID_SIGNATURE,
        error: 'Invalid callback signature'
      });
    }

    // Extract data from callback
    const { userId, transactionId, usdAmount, metadata } = extractCallbackData(networkId, callbackData);
    
    if (!userId || !transactionId || !usdAmount) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.CALLBACKS.MISSING_DATA,
        error: 'Missing userId, transactionId, or usdAmount in callback'
      });
    }

    // Process offerwall completion
    const result = await creditService.processOfferwallCompletion(
      userId,
      parseFloat(usdAmount),
      networkId,
      transactionId,
      metadata
    );

    logger.logInfo('Offerwall completion processed', {
      userId,
      networkId,
      transactionId,
      usdAmount,
      creditsEarned: result.creditsEarned
    });

    return res.status(200).json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.CALLBACKS.OFFERWALL_SUCCESS,
      data: {
        processed: true,
        creditsEarned: result.creditsEarned,
        usdAmount: result.usdAmount,
        transactionId: result.transactionId
      }
    });

  } catch (error) {
    logger.logError('Offerwall Callback Error:', error);

    if (error.message.includes('already processed')) {
      return res.status(200).json({
        code: CONSTANTS.CODE.SUCCESS,
        message: MESSAGES.CALLBACKS.ALREADY_PROCESSED,
        data: { processed: false, reason: 'already_processed' }
      });
    }

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
      error: 'Internal server error'
    });
  }
}

/**
 * Verify callback authenticity based on ad network
 */
async function verifyCallback(networkId, callbackData, req) {
  try {
    switch (networkId) {
      case 'unity':
        return verifyUnityCallback(callbackData, req);
      
      case 'admob':
        return verifyAdMobCallback(callbackData, req);
      
      case 'ironsource':
        return verifyIronSourceCallback(callbackData, req);
      
      case 'applovin':
        return verifyAppLovinCallback(callbackData, req);
      
      case 'tapjoy':
        return verifyTapjoyCallback(callbackData, req);
      
      default:
        // For unknown networks, require manual verification
        logger.logWarn('Unknown ad network callback', { networkId });
        return false;
    }
  } catch (error) {
    logger.logError('Callback verification error:', error);
    return false;
  }
}

/**
 * Unity Ads callback verification
 */
function verifyUnityCallback(data, req) {
  const { signature, ...params } = data;
  const secret = process.env.UNITY_ADS_SECRET;
  
  if (!secret || !signature) return false;
  
  // Unity uses HMAC-SHA256
  const queryString = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
  
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(queryString)
    .digest('hex');
  
  return signature === expectedSignature;
}

/**
 * AdMob callback verification
 */
function verifyAdMobCallback(data, req) {
  const { signature, ...params } = data;
  const secret = process.env.ADMOB_SECRET;
  
  if (!secret || !signature) return false;
  
  // AdMob verification logic
  const queryString = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
  
  const expectedSignature = crypto
    .createHmac('sha1', secret)
    .update(queryString)
    .digest('hex');
  
  return signature === expectedSignature;
}

/**
 * IronSource callback verification
 */
function verifyIronSourceCallback(data, req) {
  const { signature, ...params } = data;
  const secret = process.env.IRONSOURCE_SECRET;
  
  if (!secret || !signature) return false;
  
  // IronSource verification logic
  const queryString = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
  
  const expectedSignature = crypto
    .createHash('md5')
    .update(queryString + secret)
    .digest('hex');
  
  return signature === expectedSignature;
}

/**
 * AppLovin callback verification
 */
function verifyAppLovinCallback(data, req) {
  // AppLovin typically uses IP whitelisting
  const allowedIPs = (process.env.APPLOVIN_IPS || '').split(',');
  return allowedIPs.includes(req.ip);
}

/**
 * Tapjoy callback verification
 */
function verifyTapjoyCallback(data, req) {
  const { verifier, ...params } = data;
  const secret = process.env.TAPJOY_SECRET;
  
  if (!secret || !verifier) return false;
  
  // Tapjoy verification logic
  const queryString = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
  
  const expectedVerifier = crypto
    .createHash('sha1')
    .update(queryString + secret)
    .digest('hex');
  
  return verifier === expectedVerifier;
}

/**
 * Extract user ID and transaction data from callback
 */
function extractCallbackData(networkId, data) {
  switch (networkId) {
    case 'unity':
      return {
        userId: data.userId || data.user_id,
        transactionId: data.transactionId || data.transaction_id,
        usdAmount: data.revenue || data.amount,
        metadata: {
          placementId: data.placementId,
          adType: data.adType
        }
      };
    
    case 'admob':
      return {
        userId: data.user_id,
        transactionId: data.transaction_id,
        usdAmount: data.value,
        metadata: {
          adUnitId: data.ad_unit_id,
          currency: data.currency
        }
      };
    
    case 'ironsource':
      return {
        userId: data.applicationUserId,
        transactionId: data.eventId,
        usdAmount: data.rewards,
        metadata: {
          instanceId: data.instanceId,
          placementName: data.placementName
        }
      };
    
    case 'applovin':
      return {
        userId: data.user_id,
        transactionId: data.transaction_id,
        usdAmount: data.revenue,
        metadata: {
          zone: data.zone,
          country: data.country
        }
      };
    
    case 'tapjoy':
      return {
        userId: data.snuid,
        transactionId: data.id,
        usdAmount: data.currency,
        metadata: {
          placementName: data.placement_name
        }
      };
    
    default:
      return {
        userId: data.userId || data.user_id,
        transactionId: data.transactionId || data.transaction_id || data.id,
        usdAmount: data.amount || data.revenue || data.value,
        metadata: data
      };
  }
}

module.exports = {
  videoAdCallback,
  offerwallCallback
};
