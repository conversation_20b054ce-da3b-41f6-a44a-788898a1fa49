const telegramProxyService = require('../../../services/telegramProxy');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * Proxy endpoint for Telegram Bot API
 * Handles all HTTP methods and forwards them to Telegram
 */
module.exports = async (req, res) => {
  try {
    // Extract the Telegram API path from the request
    // Only support short URL format: /bot{TOKEN}/{METHOD}
    let telegramPath = req.path;

    // Add leading slash if missing
    if (telegramPath && !telegramPath.startsWith('/')) {
      telegramPath = '/' + telegramPath;
    }

    // Ensure path starts with /bot
    if (!telegramPath.startsWith('/bot')) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.PROXY.INVALID_PATH,
        error: `Telegram API path must start with /bot{token}/. Got: ${telegramPath}`
      });
    }

    // Create a new request object with the Telegram path
    const proxyReq = {
      ...req,
      path: telegramPath,
      originalPath: req.path
    };

    // Check if this is a file upload request
    const contentType = req.get('Content-Type') || '';
    if (contentType.includes('multipart/form-data')) {
      return await telegramProxyService.proxyFileUpload(proxyReq, res);
    } else {
      return await telegramProxyService.proxyRequest(proxyReq, res);
    }

  } catch (error) {
    logger.logError('Proxy Route Error:', error);

    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
      error: 'Internal server error in proxy route.'
    });
  }
};
