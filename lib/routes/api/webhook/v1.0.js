const webhookService = require('../../../services/webhookService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * Webhook management routes
 */

/**
 * Set up webhook for a bot
 * POST /api/v1.0/webhook/setup
 */
async function setupWebhook(req, res) {
  try {
    const {
      botToken,
      targetUrl,
      secretToken,
      maxConnections,
      allowedUpdates,
      dropPendingUpdates
    } = req.body;

    // Validate required fields
    if (!botToken || !targetUrl) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.WEBHOOK.MISSING_REQUIRED_FIELDS,
        error: 'Bot token and target URL are required'
      });
    }

    // Extract bot token from full token if needed
    const cleanBotToken = botToken.replace(/^bot/, '');

    const result = await webhookService.setupWebhook({
      apiKeyId: req.apiKey.keyId,
      userId: req.apiKey.userId,
      botToken: cleanBotToken,
      targetUrl,
      secretToken,
      maxConnections,
      allowedUpdates,
      dropPendingUpdates
    });

    return res.status(200).json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.WEBHOOK.SETUP_SUCCESS,
      data: result
    });

  } catch (error) {
    logger.logError('Setup Webhook Error:', error);
    
    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
      error: error.message
    });
  }
}

/**
 * Remove webhook for a bot
 * POST /api/v1.0/webhook/remove
 */
async function removeWebhook(req, res) {
  try {
    const { botToken } = req.body;

    if (!botToken) {
      return res.status(400).json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.WEBHOOK.MISSING_BOT_TOKEN,
        error: 'Bot token is required'
      });
    }

    const cleanBotToken = botToken.replace(/^bot/, '');

    // Verify ownership
    const webhook = await WebhookModel.findOne({ 
      botToken: cleanBotToken,
      apiKeyId: req.apiKey.keyId 
    });

    if (!webhook) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: MESSAGES.WEBHOOK.NOT_FOUND,
        error: 'Webhook not found or not owned by this API key'
      });
    }

    const result = await webhookService.removeWebhook(cleanBotToken);

    return res.status(200).json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.WEBHOOK.REMOVE_SUCCESS,
      data: result
    });

  } catch (error) {
    logger.logError('Remove Webhook Error:', error);
    
    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
      error: error.message
    });
  }
}

/**
 * Get webhook information
 * GET /api/v1.0/webhook/info/:botToken
 */
async function getWebhookInfo(req, res) {
  try {
    const { botToken } = req.params;
    const cleanBotToken = botToken.replace(/^bot/, '');

    const webhook = await WebhookModel.findOne({ 
      botToken: cleanBotToken,
      apiKeyId: req.apiKey.keyId 
    }).populate('userId', 'username email');

    if (!webhook) {
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: MESSAGES.WEBHOOK.NOT_FOUND,
        error: 'Webhook not found'
      });
    }

    const webhookInfo = {
      webhookId: webhook.webhookId,
      botToken: webhook.botToken.substring(0, 10) + '...',
      targetUrl: webhook.targetUrl,
      proxyUrl: webhook.proxyUrl,
      status: webhook.status,
      healthStatus: webhook.healthStatus,
      maxConnections: webhook.maxConnections,
      allowedUpdates: webhook.allowedUpdates,
      statistics: {
        totalUpdates: webhook.totalUpdates,
        successfulDeliveries: webhook.successfulDeliveries,
        failedDeliveries: webhook.failedDeliveries,
        successRate: webhook.getSuccessRate(),
        consecutiveFailures: webhook.consecutiveFailures
      },
      timestamps: {
        createdAt: webhook.createdAt,
        activatedAt: webhook.activatedAt,
        lastUpdateTime: webhook.lastUpdateTime,
        lastHealthCheck: webhook.lastHealthCheck
      }
    };

    return res.status(200).json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.WEBHOOK.INFO_SUCCESS,
      data: webhookInfo
    });

  } catch (error) {
    logger.logError('Get Webhook Info Error:', error);
    
    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
      error: error.message
    });
  }
}

/**
 * List all webhooks for an API key
 * GET /api/v1.0/webhook/list
 */
async function listWebhooks(req, res) {
  try {
    const stats = await webhookService.getWebhookStats(req.apiKey.keyId);

    return res.status(200).json({
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.WEBHOOK.LIST_SUCCESS,
      data: stats
    });

  } catch (error) {
    logger.logError('List Webhooks Error:', error);
    
    return res.status(500).json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
      error: error.message
    });
  }
}

/**
 * Handle incoming webhook from Telegram
 * POST /webhook/:webhookId
 */
async function handleIncomingWebhook(req, res) {
  try {
    const { webhookId } = req.params;
    const update = req.body;

    // Log webhook reception (without sensitive data)
    logger.logInfo('Webhook received', {
      webhookId,
      updateType: update.message ? 'message' : 
                  update.callback_query ? 'callback_query' :
                  update.inline_query ? 'inline_query' : 'other',
      timestamp: new Date().toISOString()
    });

    const result = await webhookService.handleWebhook(webhookId, update, req);

    if (result.success) {
      return res.status(200).json({ ok: true });
    } else {
      logger.logError('Webhook delivery failed:', {
        webhookId,
        error: result.error
      });
      
      // Still return 200 to Telegram to avoid retries
      return res.status(200).json({ ok: true });
    }

  } catch (error) {
    logger.logError('Handle Webhook Error:', error);
    
    // Return 200 to Telegram even on error to avoid retries
    return res.status(200).json({ ok: true });
  }
}

module.exports = {
  setupWebhook,
  removeWebhook,
  getWebhookInfo,
  listWebhooks,
  handleIncomingWebhook
};
