const axios = require('axios');
const metricsService = require('./metricsService');

class AlertingService {
  constructor() {
    this.alerts = new Map(); // Active alerts
    this.alertHistory = []; // Alert history
    this.thresholds = {
      cpu: { warning: 70, critical: 90 },
      memory: { warning: 80, critical: 90 },
      errorRate: { warning: 5, critical: 10 },
      responseTime: { warning: 1000, critical: 5000 },
      diskSpace: { warning: 80, critical: 95 }
    };
    
    this.channels = {
      email: {
        enabled: false,
        recipients: []
      },
      slack: {
        enabled: false,
        webhookUrl: null,
        channel: '#alerts'
      },
      webhook: {
        enabled: false,
        url: null
      }
    };
    
    // Start monitoring
    this.startMonitoring();
  }

  /**
   * Configure alert channels
   */
  configureChannels(config) {
    this.channels = { ...this.channels, ...config };
  }

  /**
   * Configure alert thresholds
   */
  configureThresholds(thresholds) {
    this.thresholds = { ...this.thresholds, ...thresholds };
  }

  /**
   * Start monitoring and alerting
   */
  startMonitoring() {
    // Check metrics every minute
    setInterval(() => {
      this.checkMetrics();
    }, 60000);

    // Clean old alert history every hour
    setInterval(() => {
      this.cleanAlertHistory();
    }, 3600000);
  }

  /**
   * Check metrics against thresholds
   */
  async checkMetrics() {
    try {
      const metrics = metricsService.getMetrics();
      const health = metricsService.getHealthStatus();

      // Check CPU usage
      await this.checkThreshold('cpu', metrics.system.cpuUsage, '%');

      // Check memory usage
      await this.checkThreshold('memory', metrics.system.memoryUsage, '%');

      // Check error rate
      await this.checkThreshold('errorRate', metrics.application.errorRate, '%');

      // Check response time
      await this.checkThreshold('responseTime', metrics.application.avgResponseTime, 'ms');

      // Check overall health status
      if (health.status === 'critical') {
        await this.triggerAlert('system_health', 'critical', 'System health is critical', {
          checks: health.checks,
          metrics: {
            cpu: metrics.system.cpuUsage,
            memory: metrics.system.memoryUsage,
            errorRate: metrics.application.errorRate
          }
        });
      }

    } catch (error) {
      logger.logError('Alerting check error:', error);
    }
  }

  /**
   * Check individual threshold
   */
  async checkThreshold(metric, value, unit) {
    const threshold = this.thresholds[metric];
    if (!threshold) return;

    const alertKey = `${metric}_threshold`;

    if (value >= threshold.critical) {
      await this.triggerAlert(alertKey, 'critical', 
        `${metric} is critically high: ${value}${unit}`, { value, threshold: threshold.critical });
    } else if (value >= threshold.warning) {
      await this.triggerAlert(alertKey, 'warning', 
        `${metric} is high: ${value}${unit}`, { value, threshold: threshold.warning });
    } else {
      // Clear alert if value is back to normal
      await this.clearAlert(alertKey);
    }
  }

  /**
   * Trigger an alert
   */
  async triggerAlert(alertKey, severity, message, metadata = {}) {
    const existingAlert = this.alerts.get(alertKey);
    const now = Date.now();

    // Check if this is a new alert or escalation
    if (!existingAlert) {
      // New alert
      const alert = {
        key: alertKey,
        severity,
        message,
        metadata,
        firstTriggered: now,
        lastTriggered: now,
        count: 1,
        status: 'active'
      };

      this.alerts.set(alertKey, alert);
      await this.sendAlert(alert);
      this.addToHistory(alert, 'triggered');

    } else if (existingAlert.severity !== severity) {
      // Escalation
      existingAlert.severity = severity;
      existingAlert.message = message;
      existingAlert.metadata = metadata;
      existingAlert.lastTriggered = now;
      existingAlert.count += 1;

      await this.sendAlert(existingAlert, 'escalated');
      this.addToHistory(existingAlert, 'escalated');

    } else {
      // Update existing alert
      existingAlert.lastTriggered = now;
      existingAlert.count += 1;
      existingAlert.metadata = metadata;
    }
  }

  /**
   * Clear an alert
   */
  async clearAlert(alertKey) {
    const alert = this.alerts.get(alertKey);
    if (alert) {
      alert.status = 'resolved';
      alert.resolvedAt = Date.now();
      
      await this.sendAlert(alert, 'resolved');
      this.addToHistory(alert, 'resolved');
      
      this.alerts.delete(alertKey);
    }
  }

  /**
   * Send alert to configured channels
   */
  async sendAlert(alert, action = 'triggered') {
    const alertData = {
      ...alert,
      action,
      timestamp: new Date().toISOString(),
      service: 'Telegram Proxy Service'
    };

    // Send to email
    if (this.channels.email.enabled) {
      await this.sendEmailAlert(alertData);
    }

    // Send to Slack
    if (this.channels.slack.enabled) {
      await this.sendSlackAlert(alertData);
    }

    // Send to webhook
    if (this.channels.webhook.enabled) {
      await this.sendWebhookAlert(alertData);
    }

    // Log alert
    logger.logError(`Alert ${action}:`, alertData);
  }

  /**
   * Send email alert
   */
  async sendEmailAlert(alert) {
    try {
      // This would integrate with your email service
      // For now, just log
      logger.logInfo('Email alert would be sent:', {
        recipients: this.channels.email.recipients,
        subject: `[${alert.severity.toUpperCase()}] ${alert.message}`,
        alert
      });
    } catch (error) {
      logger.logError('Email alert error:', error);
    }
  }

  /**
   * Send Slack alert
   */
  async sendSlackAlert(alert) {
    try {
      if (!this.channels.slack.webhookUrl) return;

      const color = {
        critical: 'danger',
        warning: 'warning',
        resolved: 'good'
      }[alert.severity] || 'warning';

      const payload = {
        channel: this.channels.slack.channel,
        username: 'Telegram Proxy Monitor',
        icon_emoji: ':warning:',
        attachments: [{
          color,
          title: `${alert.action.toUpperCase()}: ${alert.message}`,
          fields: [
            {
              title: 'Severity',
              value: alert.severity.toUpperCase(),
              short: true
            },
            {
              title: 'Service',
              value: alert.service,
              short: true
            },
            {
              title: 'Count',
              value: alert.count.toString(),
              short: true
            },
            {
              title: 'First Triggered',
              value: new Date(alert.firstTriggered).toISOString(),
              short: true
            }
          ],
          footer: 'Telegram Proxy Service',
          ts: Math.floor(alert.lastTriggered / 1000)
        }]
      };

      await axios.post(this.channels.slack.webhookUrl, payload);

    } catch (error) {
      logger.logError('Slack alert error:', error);
    }
  }

  /**
   * Send webhook alert
   */
  async sendWebhookAlert(alert) {
    try {
      if (!this.channels.webhook.url) return;

      await axios.post(this.channels.webhook.url, alert, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      });

    } catch (error) {
      logger.logError('Webhook alert error:', error);
    }
  }

  /**
   * Add alert to history
   */
  addToHistory(alert, action) {
    this.alertHistory.push({
      ...alert,
      action,
      timestamp: Date.now()
    });
  }

  /**
   * Clean old alert history
   */
  cleanAlertHistory() {
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    this.alertHistory = this.alertHistory.filter(alert => alert.timestamp > oneWeekAgo);
  }

  /**
   * Get active alerts
   */
  getActiveAlerts() {
    return Array.from(this.alerts.values());
  }

  /**
   * Get alert history
   */
  getAlertHistory(limit = 100) {
    return this.alertHistory
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  /**
   * Get alert statistics
   */
  getAlertStats() {
    const now = Date.now();
    const oneDayAgo = now - (24 * 60 * 60 * 1000);
    const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);

    const recentAlerts = this.alertHistory.filter(alert => alert.timestamp > oneDayAgo);
    const weeklyAlerts = this.alertHistory.filter(alert => alert.timestamp > oneWeekAgo);

    return {
      activeAlerts: this.alerts.size,
      alertsLast24h: recentAlerts.length,
      alertsLastWeek: weeklyAlerts.length,
      criticalAlertsLast24h: recentAlerts.filter(a => a.severity === 'critical').length,
      warningAlertsLast24h: recentAlerts.filter(a => a.severity === 'warning').length,
      mostFrequentAlerts: this.getMostFrequentAlerts(weeklyAlerts)
    };
  }

  /**
   * Get most frequent alerts
   */
  getMostFrequentAlerts(alerts) {
    const frequency = {};
    alerts.forEach(alert => {
      frequency[alert.key] = (frequency[alert.key] || 0) + 1;
    });

    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([key, count]) => ({ key, count }));
  }

  /**
   * Test alert system
   */
  async testAlert() {
    await this.triggerAlert('test_alert', 'warning', 'This is a test alert', {
      test: true,
      timestamp: Date.now()
    });
  }
}

// Create singleton instance
const alertingService = new AlertingService();

module.exports = alertingService;
