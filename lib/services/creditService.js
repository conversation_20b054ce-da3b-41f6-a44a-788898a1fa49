const mongoose = require('mongoose');

// Import models (they should be loaded globally, but explicit import for safety)
const UserModel = global.UserModel || require('../models/user');
const CreditHistoryModel = global.CreditHistoryModel || require('../models/creditHistory');
const SystemConfigModel = global.SystemConfigModel || require('../models/systemConfig');

class CreditService {
  constructor() {
    this.configCache = {};
    this.cacheExpiry = 0;
  }

  /**
   * Get system configuration with caching
   */
  async getConfig(key, defaultValue = null) {
    try {
      return await SystemConfigModel.getCachedValue(key, defaultValue);
    } catch (error) {
      logger.logError('Credit Service - Config error:', error);
      return defaultValue;
    }
  }

  /**
   * Check if user has enough credits for a request
   */
  async checkCredits(userId, amount = 1) {
    try {
      const user = await UserModel.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      return {
        hasEnough: user.hasEnoughCredits(amount),
        totalCredits: user.getTotalCredits(),
        monthlyCredits: user.monthlyCreditBalance,
        adCredits: user.adCreditBalance,
        required: amount
      };
    } catch (error) {
      logger.logError('Credit Service - Check credits error:', error);
      throw error;
    }
  }

  /**
   * Deduct credits for API request usage
   */
  async deductCreditsForRequest(userId, apiKeyId, requestDetails, amount = 1) {
    const session = await mongoose.startSession();

    try {
      await session.withTransaction(async () => {
        // Get user with session
        const user = await UserModel.findById(userId).session(session);
        if (!user) {
          throw new Error('User not found');
        }

        // Check if credits are available
        if (!user.hasEnoughCredits(amount)) {
          throw new Error('Insufficient credits');
        }

        // Record balance before deduction
        const balanceBefore = {
          total: user.getTotalCredits(),
          monthly: user.monthlyCreditBalance,
          ad: user.adCreditBalance
        };

        // Deduct credits
        const deducted = user.deductCredits(amount);

        // Save user
        await user.save({ session });

        // Record balance after deduction
        const balanceAfter = {
          total: user.getTotalCredits(),
          monthly: user.monthlyCreditBalance,
          ad: user.adCreditBalance
        };

        // Record transaction in credit history
        const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const transaction = new CreditHistoryModel({
          transactionId,
          userId,
          type: 'subtract',
          source: 'usage',
          amount,
          creditType: 'total',
          balanceBefore,
          balanceAfter,
          apiKeyId,
          requestDetails
        });
        await transaction.save({ session });

        return {
          success: true,
          deducted,
          balanceAfter,
          transactionId: `usage_${Date.now()}_${userId}`
        };
      });

      return { success: true };

    } catch (error) {
      logger.logError('Credit Service - Deduct credits error:', error);
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Add credits to user account
   */
  async addCredits(userId, amount, source, options = {}) {
    const {
      creditType = 'ad',
      externalTransactionId = null,
      adNetworkId = null,
      metadata = {},
      adminUserId = null,
      adminReason = null
    } = options;

    const session = await mongoose.startSession();

    try {
      let result;

      await session.withTransaction(async () => {
        // Get user with session
        const user = await UserModel.findById(userId).session(session);
        if (!user) {
          throw new Error('User not found');
        }

        // Record balance before addition
        const balanceBefore = {
          total: user.getTotalCredits(),
          monthly: user.monthlyCreditBalance,
          ad: user.adCreditBalance
        };

        // Add credits
        user.addCredits(amount, creditType);

        // Save user
        await user.save({ session });

        // Record balance after addition
        const balanceAfter = {
          total: user.getTotalCredits(),
          monthly: user.monthlyCreditBalance,
          ad: user.adCreditBalance
        };

        // Record transaction in credit history
        const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const transaction = new CreditHistoryModel({
          transactionId,
          userId,
          type: 'add',
          source,
          amount,
          creditType,
          balanceBefore,
          balanceAfter,
          externalTransactionId,
          adNetworkId,
          metadata,
          adminUserId,
          adminReason
        });
        await transaction.save({ session });

        result = {
          success: true,
          balanceAfter,
          transactionId: transaction.transactionId
        };
      });

      return result;

    } catch (error) {
      logger.logError('Credit Service - Add credits error:', error);
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Process video ad completion
   */
  async processVideoAdCompletion(userId, adNetworkId, externalTransactionId) {
    try {
      // Get configuration
      const creditAmount = await this.getConfig('video_ad_credit_amount', 10000);
      const dailyLimit = await this.getConfig('video_ad_daily_limit', 10);

      // Get user
      const user = await UserModel.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Check daily limit
      if (!user.canViewAd(dailyLimit)) {
        throw new Error('Daily ad view limit exceeded');
      }

      // Check for duplicate transaction
      const existingTransaction = await CreditHistoryModel.findOne({
        externalTransactionId,
        adNetworkId,
        source: 'video_ad'
      });

      if (existingTransaction) {
        throw new Error('Transaction already processed');
      }

      // Record ad view
      user.recordAdView();
      await user.save();

      // Add credits
      const result = await this.addCredits(userId, creditAmount, 'video_ad', {
        creditType: 'ad',
        externalTransactionId,
        adNetworkId,
        metadata: {
          dailyAdCount: user.dailyAdViewsCount,
          adViewDate: new Date()
        }
      });

      return {
        ...result,
        creditsEarned: creditAmount,
        dailyAdViewsRemaining: Math.max(0, dailyLimit - user.dailyAdViewsCount)
      };

    } catch (error) {
      logger.logError('Credit Service - Video ad completion error:', error);
      throw error;
    }
  }

  /**
   * Process offerwall completion
   */
  async processOfferwallCompletion(userId, usdAmount, adNetworkId, externalTransactionId, metadata = {}) {
    try {
      // Get configuration
      const usdToRequestRatio = await this.getConfig('offerwall_credit_usd_to_request_ratio', 1000000);

      // Calculate credits
      const creditAmount = Math.floor(usdAmount * usdToRequestRatio);

      if (creditAmount <= 0) {
        throw new Error('Invalid USD amount');
      }

      // Check for duplicate transaction
      const existingTransaction = await CreditHistoryModel.findOne({
        externalTransactionId,
        adNetworkId,
        source: 'offerwall'
      });

      if (existingTransaction) {
        throw new Error('Transaction already processed');
      }

      // Add credits
      const result = await this.addCredits(userId, creditAmount, 'offerwall', {
        creditType: 'ad',
        externalTransactionId,
        adNetworkId,
        metadata: {
          usdAmount,
          usdToRequestRatio,
          ...metadata
        }
      });

      return {
        ...result,
        creditsEarned: creditAmount,
        usdAmount,
        conversionRate: usdToRequestRatio
      };

    } catch (error) {
      logger.logError('Credit Service - Offerwall completion error:', error);
      throw error;
    }
  }

  /**
   * Reset monthly credits for all users
   */
  async resetMonthlyCredits() {
    try {
      const creditAmount = await this.getConfig('monthly_credit_amount', 100000);

      logger.logInfo('Starting monthly credit reset', { creditAmount });

      // Get users that need reset
      const users = await UserModel.getUsersForMonthlyReset();

      const results = [];
      let successCount = 0;
      let errorCount = 0;

      for (const user of users) {
        try {
          const balanceBefore = {
            total: user.getTotalCredits(),
            monthly: user.monthlyCreditBalance,
            ad: user.adCreditBalance
          };

          // Reset monthly credits
          user.resetMonthlyCredits(creditAmount);
          await user.save();

          const balanceAfter = {
            total: user.getTotalCredits(),
            monthly: user.monthlyCreditBalance,
            ad: user.adCreditBalance
          };

          // Record transaction
          await CreditHistoryModel.recordTransaction({
            userId: user._id,
            type: 'add',
            source: 'monthly_bonus',
            amount: creditAmount,
            creditType: 'monthly',
            balanceBefore,
            balanceAfter,
            metadata: {
              resetDate: new Date(),
              previousBalance: balanceBefore.monthly
            }
          });

          results.push({
            userId: user._id,
            username: user.username,
            success: true,
            oldBalance: balanceBefore.monthly,
            newBalance: balanceAfter.monthly
          });

          successCount++;

        } catch (error) {
          logger.logError('Monthly reset error for user:', { userId: user._id, error: error.message });

          results.push({
            userId: user._id,
            username: user.username,
            success: false,
            error: error.message
          });

          errorCount++;
        }
      }

      logger.logInfo('Monthly credit reset completed', {
        totalUsers: users.length,
        successCount,
        errorCount
      });

      return {
        success: true,
        totalUsers: users.length,
        successCount,
        errorCount,
        creditAmount,
        results
      };

    } catch (error) {
      logger.logError('Credit Service - Monthly reset error:', error);
      throw error;
    }
  }

  /**
   * Get user credit summary
   */
  async getUserCreditSummary(userId) {
    try {
      const user = await UserModel.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Get recent transactions
      const recentTransactions = await CreditHistoryModel.getUserHistory(userId, {
        limit: 10
      });

      // Get monthly summary
      const monthlySummary = await CreditHistoryModel.getUserSummary(userId, 30);

      return {
        credits: user.getCreditSummary(),
        recentTransactions,
        monthlySummary,
        limits: {
          dailyAdLimit: await this.getConfig('video_ad_daily_limit', 10),
          dailyAdViewsRemaining: Math.max(0,
            await this.getConfig('video_ad_daily_limit', 10) - user.dailyAdViewsCount
          )
        }
      };

    } catch (error) {
      logger.logError('Credit Service - Get summary error:', error);
      throw error;
    }
  }

  /**
   * Admin function to adjust user credits
   */
  async adminAdjustCredits(userId, amount, reason, adminUserId) {
    try {
      const source = amount > 0 ? 'admin_adjustment' : 'admin_adjustment';
      const type = amount > 0 ? 'add' : 'subtract';
      const absoluteAmount = Math.abs(amount);

      if (type === 'add') {
        return await this.addCredits(userId, absoluteAmount, source, {
          creditType: 'ad',
          adminUserId,
          adminReason: reason
        });
      } else {
        // For deduction, we need special handling
        const session = await mongoose.startSession();

        try {
          let result;

          await session.withTransaction(async () => {
            const user = await UserModel.findById(userId).session(session);
            if (!user) {
              throw new Error('User not found');
            }

            const balanceBefore = {
              total: user.getTotalCredits(),
              monthly: user.monthlyCreditBalance,
              ad: user.adCreditBalance
            };

            // Force deduction (even if insufficient)
            let remaining = absoluteAmount;

            // Deduct from ad credits first for admin adjustments
            if (remaining > 0 && user.adCreditBalance > 0) {
              const adDeduction = Math.min(remaining, user.adCreditBalance);
              user.adCreditBalance -= adDeduction;
              remaining -= adDeduction;
            }

            // Then from monthly credits
            if (remaining > 0 && user.monthlyCreditBalance > 0) {
              const monthlyDeduction = Math.min(remaining, user.monthlyCreditBalance);
              user.monthlyCreditBalance -= monthlyDeduction;
              remaining -= monthlyDeduction;
            }

            user.totalCreditsUsed += (absoluteAmount - remaining);
            user.updatedAt = Date.now();

            await user.save({ session });

            const balanceAfter = {
              total: user.getTotalCredits(),
              monthly: user.monthlyCreditBalance,
              ad: user.adCreditBalance
            };

            await CreditHistoryModel.recordTransaction({
              userId,
              type: 'subtract',
              source,
              amount: absoluteAmount - remaining,
              creditType: 'total',
              balanceBefore,
              balanceAfter,
              adminUserId,
              adminReason: reason
            });

            result = {
              success: true,
              balanceAfter,
              actualDeducted: absoluteAmount - remaining,
              requestedDeduction: absoluteAmount
            };
          });

          return result;

        } finally {
          await session.endSession();
        }
      }

    } catch (error) {
      logger.logError('Credit Service - Admin adjust error:', error);
      throw error;
    }
  }
}

// Create singleton instance
const creditService = new CreditService();

module.exports = creditService;
