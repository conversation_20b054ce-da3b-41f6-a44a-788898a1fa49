const os = require('os');
const process = require('process');

class MetricsService {
  constructor() {
    this.metrics = {
      // System metrics
      system: {
        cpuUsage: 0,
        memoryUsage: 0,
        freeMemory: 0,
        totalMemory: 0,
        loadAverage: [],
        uptime: 0
      },
      
      // Application metrics
      application: {
        uptime: 0,
        requestsTotal: 0,
        requestsPerSecond: 0,
        activeConnections: 0,
        errorRate: 0,
        avgResponseTime: 0
      },
      
      // Proxy specific metrics
      proxy: {
        totalProxyRequests: 0,
        successfulProxyRequests: 0,
        failedProxyRequests: 0,
        telegramApiErrors: 0,
        rateLimitHits: 0,
        authFailures: 0
      },
      
      // API key metrics
      apiKeys: {
        totalApiKeys: 0,
        activeApiKeys: 0,
        suspendedApiKeys: 0,
        revokedApiKeys: 0
      },
      
      // Webhook metrics
      webhooks: {
        totalWebhooks: 0,
        activeWebhooks: 0,
        webhookDeliveries: 0,
        failedDeliveries: 0,
        avgDeliveryTime: 0
      },
      
      // Database metrics
      database: {
        mongoConnections: 0,
        redisConnections: 0,
        queryTime: 0,
        errorCount: 0
      }
    };
    
    this.requestHistory = [];
    this.errorHistory = [];
    this.responseTimeHistory = [];
    this.startTime = Date.now();
    
    // Start metrics collection
    this.startCollection();
  }

  /**
   * Start metrics collection
   */
  startCollection() {
    // Collect system metrics every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Collect application metrics every 10 seconds
    setInterval(() => {
      this.collectApplicationMetrics();
    }, 10000);

    // Collect database metrics every 60 seconds
    setInterval(() => {
      this.collectDatabaseMetrics();
    }, 60000);

    // Clean old history every 5 minutes
    setInterval(() => {
      this.cleanHistory();
    }, 300000);
  }

  /**
   * Collect system metrics
   */
  collectSystemMetrics() {
    try {
      // CPU usage
      const cpus = os.cpus();
      let totalIdle = 0;
      let totalTick = 0;

      cpus.forEach(cpu => {
        for (const type in cpu.times) {
          totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
      });

      this.metrics.system.cpuUsage = 100 - (totalIdle / totalTick * 100);

      // Memory usage
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;

      this.metrics.system.totalMemory = totalMemory;
      this.metrics.system.freeMemory = freeMemory;
      this.metrics.system.memoryUsage = (usedMemory / totalMemory) * 100;

      // Load average
      this.metrics.system.loadAverage = os.loadavg();

      // System uptime
      this.metrics.system.uptime = os.uptime();

    } catch (error) {
      logger.logError('System metrics collection error:', error);
    }
  }

  /**
   * Collect application metrics
   */
  collectApplicationMetrics() {
    try {
      // Application uptime
      this.metrics.application.uptime = (Date.now() - this.startTime) / 1000;

      // Process memory usage
      const memUsage = process.memoryUsage();
      this.metrics.application.memoryUsage = {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external
      };

      // Calculate requests per second (last minute)
      const oneMinuteAgo = Date.now() - 60000;
      const recentRequests = this.requestHistory.filter(req => req.timestamp > oneMinuteAgo);
      this.metrics.application.requestsPerSecond = recentRequests.length / 60;

      // Calculate error rate (last hour)
      const oneHourAgo = Date.now() - 3600000;
      const recentErrors = this.errorHistory.filter(err => err.timestamp > oneHourAgo);
      const recentTotalRequests = this.requestHistory.filter(req => req.timestamp > oneHourAgo);
      
      if (recentTotalRequests.length > 0) {
        this.metrics.application.errorRate = (recentErrors.length / recentTotalRequests.length) * 100;
      }

      // Calculate average response time (last hour)
      const recentResponseTimes = this.responseTimeHistory.filter(rt => rt.timestamp > oneHourAgo);
      if (recentResponseTimes.length > 0) {
        const avgTime = recentResponseTimes.reduce((sum, rt) => sum + rt.time, 0) / recentResponseTimes.length;
        this.metrics.application.avgResponseTime = avgTime;
      }

    } catch (error) {
      logger.logError('Application metrics collection error:', error);
    }
  }

  /**
   * Collect database metrics
   */
  async collectDatabaseMetrics() {
    try {
      // MongoDB metrics
      const mongoStats = await this.getMongoStats();
      this.metrics.database.mongoConnections = mongoStats.connections;

      // Redis metrics
      const redisStats = await this.getRedisStats();
      this.metrics.database.redisConnections = redisStats.connections;

      // API key metrics
      const apiKeyStats = await this.getApiKeyStats();
      this.metrics.apiKeys = apiKeyStats;

      // Webhook metrics
      const webhookStats = await this.getWebhookStats();
      this.metrics.webhooks = webhookStats;

    } catch (error) {
      logger.logError('Database metrics collection error:', error);
    }
  }

  /**
   * Record request
   */
  recordRequest(method, path, statusCode, responseTime, apiKeyId = null) {
    const timestamp = Date.now();
    
    this.requestHistory.push({
      timestamp,
      method,
      path,
      statusCode,
      responseTime,
      apiKeyId
    });

    this.responseTimeHistory.push({
      timestamp,
      time: responseTime
    });

    this.metrics.application.requestsTotal += 1;

    // Record errors
    if (statusCode >= 400) {
      this.errorHistory.push({
        timestamp,
        statusCode,
        method,
        path,
        apiKeyId
      });

      if (statusCode >= 500) {
        this.metrics.proxy.failedProxyRequests += 1;
      }
    } else {
      this.metrics.proxy.successfulProxyRequests += 1;
    }

    this.metrics.proxy.totalProxyRequests += 1;
  }

  /**
   * Record specific events
   */
  recordRateLimitHit() {
    this.metrics.proxy.rateLimitHits += 1;
  }

  recordAuthFailure() {
    this.metrics.proxy.authFailures += 1;
  }

  recordTelegramApiError() {
    this.metrics.proxy.telegramApiErrors += 1;
  }

  recordWebhookDelivery(success, deliveryTime) {
    this.metrics.webhooks.webhookDeliveries += 1;
    
    if (!success) {
      this.metrics.webhooks.failedDeliveries += 1;
    }

    // Update average delivery time
    const currentAvg = this.metrics.webhooks.avgDeliveryTime;
    const totalDeliveries = this.metrics.webhooks.webhookDeliveries;
    this.metrics.webhooks.avgDeliveryTime = 
      ((currentAvg * (totalDeliveries - 1)) + deliveryTime) / totalDeliveries;
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      timestamp: new Date().toISOString(),
      collectionTime: Date.now()
    };
  }

  /**
   * Get Prometheus format metrics
   */
  getPrometheusMetrics() {
    const metrics = this.getMetrics();
    let output = '';

    // System metrics
    output += `# HELP system_cpu_usage_percent CPU usage percentage\n`;
    output += `# TYPE system_cpu_usage_percent gauge\n`;
    output += `system_cpu_usage_percent ${metrics.system.cpuUsage}\n\n`;

    output += `# HELP system_memory_usage_percent Memory usage percentage\n`;
    output += `# TYPE system_memory_usage_percent gauge\n`;
    output += `system_memory_usage_percent ${metrics.system.memoryUsage}\n\n`;

    // Application metrics
    output += `# HELP app_requests_total Total number of requests\n`;
    output += `# TYPE app_requests_total counter\n`;
    output += `app_requests_total ${metrics.application.requestsTotal}\n\n`;

    output += `# HELP app_requests_per_second Requests per second\n`;
    output += `# TYPE app_requests_per_second gauge\n`;
    output += `app_requests_per_second ${metrics.application.requestsPerSecond}\n\n`;

    output += `# HELP app_error_rate_percent Error rate percentage\n`;
    output += `# TYPE app_error_rate_percent gauge\n`;
    output += `app_error_rate_percent ${metrics.application.errorRate}\n\n`;

    // Proxy metrics
    output += `# HELP proxy_requests_total Total proxy requests\n`;
    output += `# TYPE proxy_requests_total counter\n`;
    output += `proxy_requests_total ${metrics.proxy.totalProxyRequests}\n\n`;

    output += `# HELP proxy_rate_limit_hits Rate limit hits\n`;
    output += `# TYPE proxy_rate_limit_hits counter\n`;
    output += `proxy_rate_limit_hits ${metrics.proxy.rateLimitHits}\n\n`;

    return output;
  }

  /**
   * Get health status
   */
  getHealthStatus() {
    const metrics = this.getMetrics();
    const health = {
      status: 'healthy',
      checks: {},
      timestamp: new Date().toISOString()
    };

    // CPU check
    if (metrics.system.cpuUsage > 90) {
      health.status = 'critical';
      health.checks.cpu = 'critical';
    } else if (metrics.system.cpuUsage > 70) {
      health.status = health.status === 'healthy' ? 'warning' : health.status;
      health.checks.cpu = 'warning';
    } else {
      health.checks.cpu = 'healthy';
    }

    // Memory check
    if (metrics.system.memoryUsage > 90) {
      health.status = 'critical';
      health.checks.memory = 'critical';
    } else if (metrics.system.memoryUsage > 80) {
      health.status = health.status === 'healthy' ? 'warning' : health.status;
      health.checks.memory = 'warning';
    } else {
      health.checks.memory = 'healthy';
    }

    // Error rate check
    if (metrics.application.errorRate > 10) {
      health.status = 'critical';
      health.checks.errorRate = 'critical';
    } else if (metrics.application.errorRate > 5) {
      health.status = health.status === 'healthy' ? 'warning' : health.status;
      health.checks.errorRate = 'warning';
    } else {
      health.checks.errorRate = 'healthy';
    }

    return health;
  }

  /**
   * Clean old history data
   */
  cleanHistory() {
    const oneHourAgo = Date.now() - 3600000;
    
    this.requestHistory = this.requestHistory.filter(req => req.timestamp > oneHourAgo);
    this.errorHistory = this.errorHistory.filter(err => err.timestamp > oneHourAgo);
    this.responseTimeHistory = this.responseTimeHistory.filter(rt => rt.timestamp > oneHourAgo);
  }

  /**
   * Helper methods for database stats
   */
  async getMongoStats() {
    try {
      // This would need to be implemented based on your MongoDB setup
      return { connections: 1 }; // Placeholder
    } catch {
      return { connections: 0 };
    }
  }

  async getRedisStats() {
    try {
      // This would need to be implemented based on your Redis setup
      return { connections: 1 }; // Placeholder
    } catch {
      return { connections: 0 };
    }
  }

  async getApiKeyStats() {
    try {
      const total = await ApiKeyModel.countDocuments();
      const active = await ApiKeyModel.countDocuments({ status: 'active' });
      const suspended = await ApiKeyModel.countDocuments({ status: 'suspended' });
      const revoked = await ApiKeyModel.countDocuments({ status: 'revoked' });

      return {
        totalApiKeys: total,
        activeApiKeys: active,
        suspendedApiKeys: suspended,
        revokedApiKeys: revoked
      };
    } catch {
      return {
        totalApiKeys: 0,
        activeApiKeys: 0,
        suspendedApiKeys: 0,
        revokedApiKeys: 0
      };
    }
  }

  async getWebhookStats() {
    try {
      const total = await WebhookModel.countDocuments();
      const active = await WebhookModel.countDocuments({ status: 'active' });

      return {
        totalWebhooks: total,
        activeWebhooks: active,
        webhookDeliveries: this.metrics.webhooks.webhookDeliveries,
        failedDeliveries: this.metrics.webhooks.failedDeliveries,
        avgDeliveryTime: this.metrics.webhooks.avgDeliveryTime
      };
    } catch {
      return {
        totalWebhooks: 0,
        activeWebhooks: 0,
        webhookDeliveries: 0,
        failedDeliveries: 0,
        avgDeliveryTime: 0
      };
    }
  }
}

// Create singleton instance
const metricsService = new MetricsService();

module.exports = metricsService;
