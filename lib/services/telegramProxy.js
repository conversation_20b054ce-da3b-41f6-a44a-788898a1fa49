const httpClient = require('../util/httpClient');
const CONSTANTS = require('../const');
const MESSAGES = require('../message');

class TelegramProxyService {
  constructor() {
    this.supportedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    this.maxRetries = 2;
    this.retryDelay = 1000; // 1 second
  }
  
  /**
   * Proxy a request to Telegram API
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async proxyRequest(req, res) {
    try {
      const { method, path, body, query, headers } = req;
      
      // Validate HTTP method
      if (!this.supportedMethods.includes(method.toUpperCase())) {
        return res.status(405).json({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.PROXY.METHOD_NOT_ALLOWED,
          error: `HTTP method ${method} is not supported.`
        });
      }
      
      // Validate Telegram API path
      if (!httpClient.isValidTelegramPath(path)) {
        return res.status(400).json({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.PROXY.INVALID_PATH,
          error: 'Invalid Telegram API path. Path must match /bot{token}/{method} pattern.'
        });
      }
      
      // Extract bot token for validation (optional security check)
      const botToken = httpClient.extractBotToken(path);
      if (!botToken) {
        return res.status(400).json({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.PROXY.INVALID_BOT_TOKEN,
          error: 'Invalid or missing bot token in path.'
        });
      }
      
      // Prepare request data
      let requestData = null;
      if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
        requestData = body;
      }
      
      // Make request to Telegram API with retry logic
      const response = await this.makeRequestWithRetry(
        method,
        path,
        requestData,
        headers,
        query
      );
      
      // Set response headers
      this.setResponseHeaders(res, response.headers);
      
      // Send response
      return res.status(response.status).json(response.data);
      
    } catch (error) {
      logger.logError('Telegram Proxy Service Error:', error);
      
      return res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
        error: 'Internal proxy error occurred.'
      });
    }
  }
  
  /**
   * Make request with retry logic
   * @param {string} method - HTTP method
   * @param {string} path - API path
   * @param {*} data - Request data
   * @param {Object} headers - Request headers
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Response object
   */
  async makeRequestWithRetry(method, path, data, headers, params) {
    let lastError = null;
    
    for (let attempt = 1; attempt <= this.maxRetries + 1; attempt++) {
      try {
        const response = await httpClient.proxyRequest(method, path, data, headers, params);
        
        // If it's a network error and we have retries left, continue
        if (response.isNetworkError && attempt <= this.maxRetries) {
          lastError = new Error(`Network error on attempt ${attempt}`);
          await this.delay(this.retryDelay * attempt);
          continue;
        }
        
        // Return response (success or Telegram API error)
        return response;
        
      } catch (error) {
        lastError = error;
        
        // If we have retries left, wait and try again
        if (attempt <= this.maxRetries) {
          await this.delay(this.retryDelay * attempt);
          continue;
        }
        
        // No more retries, throw the error
        throw error;
      }
    }
    
    // This should not be reached, but just in case
    throw lastError || new Error('Max retries exceeded');
  }
  
  /**
   * Set appropriate response headers
   * @param {Object} res - Express response object
   * @param {Object} telegramHeaders - Headers from Telegram response
   */
  setResponseHeaders(res, telegramHeaders) {
    // Copy safe headers from Telegram response
    const safeHeaders = [
      'content-type',
      'content-length',
      'cache-control',
      'expires',
      'last-modified',
      'etag'
    ];
    
    safeHeaders.forEach(headerName => {
      if (telegramHeaders[headerName]) {
        res.set(headerName, telegramHeaders[headerName]);
      }
    });
    
    // Add proxy-specific headers
    res.set('X-Proxy-Service', 'TelegramProxy/1.0');
    res.set('X-Request-ID', res.req.requestId);
    
    // Add CORS headers if needed
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key');
  }
  
  /**
   * Handle file uploads (multipart/form-data)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async proxyFileUpload(req, res) {
    try {
      // For file uploads, we need to handle multipart/form-data
      // The body parser should already have processed this
      
      const { method, path, body, query, headers } = req;
      
      // Validate that this is a POST request for file upload
      if (method !== 'POST') {
        return res.status(405).json({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.PROXY.METHOD_NOT_ALLOWED,
          error: 'File uploads must use POST method.'
        });
      }
      
      // Check content type
      const contentType = headers['content-type'] || '';
      if (!contentType.includes('multipart/form-data')) {
        return res.status(400).json({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.PROXY.INVALID_CONTENT_TYPE,
          error: 'File uploads must use multipart/form-data content type.'
        });
      }
      
      // Proxy the file upload request
      const response = await this.makeRequestWithRetry(
        method,
        path,
        body,
        headers,
        query
      );
      
      // Set response headers
      this.setResponseHeaders(res, response.headers);
      
      // Send response
      return res.status(response.status).json(response.data);
      
    } catch (error) {
      logger.logError('File Upload Proxy Error:', error);
      
      return res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
        error: 'Internal proxy error during file upload.'
      });
    }
  }
  
  /**
   * Delay function for retry logic
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Promise that resolves after delay
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * Get proxy health status
   * @returns {Object} Health status
   */
  getHealthStatus() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'TelegramProxyService',
      version: '1.0.0'
    };
  }
}

module.exports = new TelegramProxyService();
