const axios = require('axios');
const crypto = require('crypto');
const httpClient = require('../util/httpClient');
const CONSTANTS = require('../const');
const MESSAGES = require('../message');

class WebhookService {
  constructor() {
    this.activeWebhooks = new Map(); // Cache active webhooks
    this.deliveryQueue = []; // Queue for failed deliveries
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second base delay
  }

  /**
   * Set up webhook for a bot
   * @param {Object} params - Webhook parameters
   * @returns {Promise<Object>} Webhook configuration result
   */
  async setupWebhook(params) {
    const {
      apiKeyId,
      userId,
      botToken,
      targetUrl,
      secretToken,
      maxConnections = 40,
      allowedUpdates = [],
      dropPendingUpdates = false
    } = params;

    try {
      // Generate unique webhook ID
      const webhookId = crypto.randomUUID();
      const proxyUrl = `${process.env.WEBHOOK_BASE_URL || 'https://your-domain.com'}/webhook/${webhookId}`;

      // Validate target URL
      if (!this.isValidUrl(targetUrl)) {
        throw new Error('Invalid target URL provided');
      }

      // Check if webhook already exists for this bot
      const existingWebhook = await WebhookModel.findOne({ botToken });
      if (existingWebhook) {
        // Deactivate existing webhook
        await existingWebhook.deactivate();
      }

      // Create webhook record
      const webhook = new WebhookModel({
        webhookId,
        apiKeyId,
        userId,
        botToken,
        targetUrl,
        proxyUrl,
        secretToken,
        maxConnections,
        allowedUpdates,
        dropPendingUpdates
      });

      await webhook.save();

      // Set webhook with Telegram
      const telegramResult = await this.setTelegramWebhook(
        botToken,
        proxyUrl,
        secretToken,
        maxConnections,
        allowedUpdates,
        dropPendingUpdates
      );

      if (telegramResult.success) {
        await webhook.activate();
        this.activeWebhooks.set(webhookId, webhook);
        
        return {
          success: true,
          webhookId,
          proxyUrl,
          telegramResponse: telegramResult.data
        };
      } else {
        await webhook.recordFailure(telegramResult.error);
        throw new Error(`Failed to set Telegram webhook: ${telegramResult.error}`);
      }

    } catch (error) {
      logger.logError('Webhook Setup Error:', error);
      throw error;
    }
  }

  /**
   * Remove webhook for a bot
   * @param {string} botToken - Bot token
   * @returns {Promise<Object>} Removal result
   */
  async removeWebhook(botToken) {
    try {
      // Find webhook record
      const webhook = await WebhookModel.findOne({ botToken });
      if (!webhook) {
        throw new Error('Webhook not found');
      }

      // Remove webhook from Telegram
      const telegramResult = await this.deleteTelegramWebhook(botToken);

      // Deactivate webhook record
      await webhook.deactivate();
      this.activeWebhooks.delete(webhook.webhookId);

      return {
        success: true,
        telegramResponse: telegramResult.data
      };

    } catch (error) {
      logger.logError('Webhook Removal Error:', error);
      throw error;
    }
  }

  /**
   * Handle incoming webhook from Telegram
   * @param {string} webhookId - Webhook ID
   * @param {Object} update - Telegram update object
   * @param {Object} req - Express request object
   * @returns {Promise<Object>} Delivery result
   */
  async handleWebhook(webhookId, update, req) {
    try {
      // Find webhook configuration
      const webhook = this.activeWebhooks.get(webhookId) || 
                     await WebhookModel.findOne({ webhookId, status: 'active' });

      if (!webhook) {
        throw new Error('Webhook not found or inactive');
      }

      // Verify webhook authenticity (if secret token is set)
      if (webhook.secretToken) {
        const isValid = this.verifyWebhookSignature(req, webhook.secretToken);
        if (!isValid) {
          throw new Error('Invalid webhook signature');
        }
      }

      // Deliver update to user's endpoint
      const deliveryResult = await this.deliverUpdate(webhook, update);

      if (deliveryResult.success) {
        await webhook.recordSuccess();
        return { success: true, delivered: true };
      } else {
        await webhook.recordFailure(deliveryResult.error);
        
        // Add to retry queue if delivery failed
        this.addToRetryQueue(webhook, update);
        
        return { success: false, error: deliveryResult.error };
      }

    } catch (error) {
      logger.logError('Webhook Handling Error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Deliver update to user's webhook endpoint
   * @param {Object} webhook - Webhook configuration
   * @param {Object} update - Telegram update
   * @returns {Promise<Object>} Delivery result
   */
  async deliverUpdate(webhook, update) {
    try {
      const headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'TelegramProxyService/1.0'
      };

      // Add secret token header if configured
      if (webhook.secretToken) {
        const signature = this.generateWebhookSignature(JSON.stringify(update), webhook.secretToken);
        headers['X-Telegram-Bot-Api-Secret-Token'] = webhook.secretToken;
        headers['X-Telegram-Bot-Api-Signature'] = signature;
      }

      const response = await axios.post(webhook.targetUrl, update, {
        headers,
        timeout: 10000, // 10 second timeout
        validateStatus: (status) => status >= 200 && status < 300
      });

      return {
        success: true,
        status: response.status,
        data: response.data
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        status: error.response ? error.response.status : 0
      };
    }
  }

  /**
   * Set webhook with Telegram Bot API
   * @param {string} botToken - Bot token
   * @param {string} url - Webhook URL
   * @param {string} secretToken - Secret token
   * @param {number} maxConnections - Max connections
   * @param {Array} allowedUpdates - Allowed update types
   * @param {boolean} dropPendingUpdates - Drop pending updates
   * @returns {Promise<Object>} Telegram API response
   */
  async setTelegramWebhook(botToken, url, secretToken, maxConnections, allowedUpdates, dropPendingUpdates) {
    try {
      const data = {
        url,
        max_connections: maxConnections,
        drop_pending_updates: dropPendingUpdates
      };

      if (secretToken) {
        data.secret_token = secretToken;
      }

      if (allowedUpdates && allowedUpdates.length > 0) {
        data.allowed_updates = allowedUpdates;
      }

      const response = await httpClient.proxyRequest(
        'POST',
        `/bot${botToken}/setWebhook`,
        data,
        { 'Content-Type': 'application/json' }
      );

      return {
        success: response.status === 200,
        data: response.data,
        error: response.status !== 200 ? response.data.description : null
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete webhook from Telegram Bot API
   * @param {string} botToken - Bot token
   * @returns {Promise<Object>} Telegram API response
   */
  async deleteTelegramWebhook(botToken) {
    try {
      const response = await httpClient.proxyRequest(
        'POST',
        `/bot${botToken}/deleteWebhook`,
        { drop_pending_updates: true },
        { 'Content-Type': 'application/json' }
      );

      return {
        success: response.status === 200,
        data: response.data
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Verify webhook signature
   * @param {Object} req - Express request
   * @param {string} secretToken - Secret token
   * @returns {boolean} Verification result
   */
  verifyWebhookSignature(req, secretToken) {
    const signature = req.get('X-Telegram-Bot-Api-Signature');
    if (!signature) return false;

    const expectedSignature = this.generateWebhookSignature(
      JSON.stringify(req.body),
      secretToken
    );

    return signature === expectedSignature;
  }

  /**
   * Generate webhook signature
   * @param {string} body - Request body
   * @param {string} secretToken - Secret token
   * @returns {string} Signature
   */
  generateWebhookSignature(body, secretToken) {
    return crypto
      .createHmac('sha256', secretToken)
      .update(body)
      .digest('hex');
  }

  /**
   * Validate URL format
   * @param {string} url - URL to validate
   * @returns {boolean} Validation result
   */
  isValidUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'https:' && urlObj.hostname !== 'localhost';
    } catch {
      return false;
    }
  }

  /**
   * Add failed delivery to retry queue
   * @param {Object} webhook - Webhook configuration
   * @param {Object} update - Telegram update
   */
  addToRetryQueue(webhook, update) {
    this.deliveryQueue.push({
      webhook,
      update,
      attempts: 0,
      nextRetry: Date.now() + this.retryDelay
    });
  }

  /**
   * Process retry queue
   */
  async processRetryQueue() {
    const now = Date.now();
    const readyItems = this.deliveryQueue.filter(item => item.nextRetry <= now);

    for (const item of readyItems) {
      if (item.attempts >= this.maxRetries) {
        // Remove from queue after max retries
        this.deliveryQueue = this.deliveryQueue.filter(i => i !== item);
        continue;
      }

      const result = await this.deliverUpdate(item.webhook, item.update);
      
      if (result.success) {
        // Remove from queue on success
        this.deliveryQueue = this.deliveryQueue.filter(i => i !== item);
        await item.webhook.recordSuccess();
      } else {
        // Update retry info
        item.attempts += 1;
        item.nextRetry = now + (this.retryDelay * Math.pow(2, item.attempts)); // Exponential backoff
      }
    }
  }

  /**
   * Get webhook statistics
   * @param {string} apiKeyId - API key ID
   * @returns {Promise<Object>} Webhook statistics
   */
  async getWebhookStats(apiKeyId) {
    try {
      const webhooks = await WebhookModel.findByApiKey(apiKeyId);
      
      const stats = {
        totalWebhooks: webhooks.length,
        activeWebhooks: webhooks.filter(w => w.status === 'active').length,
        totalUpdates: webhooks.reduce((sum, w) => sum + w.totalUpdates, 0),
        successfulDeliveries: webhooks.reduce((sum, w) => sum + w.successfulDeliveries, 0),
        failedDeliveries: webhooks.reduce((sum, w) => sum + w.failedDeliveries, 0),
        webhooks: webhooks.map(w => ({
          webhookId: w.webhookId,
          botToken: w.botToken.substring(0, 10) + '...',
          status: w.status,
          healthStatus: w.healthStatus,
          successRate: w.getSuccessRate(),
          totalUpdates: w.totalUpdates,
          lastUpdateTime: w.lastUpdateTime
        }))
      };

      return stats;

    } catch (error) {
      logger.logError('Get Webhook Stats Error:', error);
      throw error;
    }
  }
}

// Start retry queue processor
const webhookService = new WebhookService();
setInterval(() => {
  webhookService.processRetryQueue();
}, 5000); // Process every 5 seconds

module.exports = webhookService;
