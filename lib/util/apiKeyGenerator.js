const crypto = require('crypto');

class ApiKeyGenerator {
  /**
   * Generate a secure API key
   * @param {number} length - Length of the API key (default: 32)
   * @returns {string} Generated API key
   */
  static generateApiKey(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, chars.length);
      result += chars[randomIndex];
    }
    
    return result;
  }
  
  /**
   * Generate a prefixed API key
   * @param {string} prefix - Prefix for the API key (e.g., 'tgp_' for Telegram Proxy)
   * @param {number} length - Length of the random part (default: 32)
   * @returns {string} Generated API key with prefix
   */
  static generatePrefixedApiKey(prefix = 'tgp_', length = 32) {
    const randomPart = this.generateApiKey(length);
    return `${prefix}${randomPart}`;
  }
  
  /**
   * Validate API key format
   * @param {string} apiKey - API key to validate
   * @returns {boolean} True if valid format
   */
  static validateApiKeyFormat(apiKey) {
    if (typeof apiKey !== 'string') return false;
    if (apiKey.length < 10) return false;
    
    // Check if it contains only allowed characters
    const allowedChars = /^[A-Za-z0-9_-]+$/;
    return allowedChars.test(apiKey);
  }
  
  /**
   * Create a new API key for a user
   * @param {string} userId - User ID
   * @param {string} name - API key name
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Created API key document
   */
  static async createApiKey(userId, name, options = {}) {
    const {
      description = '',
      dailyLimit = 1000,
      monthlyLimit = 30000,
      requestsPerMinute = 60,
      requestsPerHour = 1000,
      allowedIPs = [],
      expiresAt = null
    } = options;
    
    // Generate unique API key
    let apiKey;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 10;
    
    while (!isUnique && attempts < maxAttempts) {
      apiKey = this.generatePrefixedApiKey('tgp_', 32);
      
      // Check if API key already exists
      const existing = await ApiKeyModel.findOne({ keyId: apiKey });
      if (!existing) {
        isUnique = true;
      }
      attempts++;
    }
    
    if (!isUnique) {
      throw new Error('Failed to generate unique API key after multiple attempts');
    }
    
    // Create API key document
    const apiKeyDoc = new ApiKeyModel({
      keyId: apiKey,
      userId,
      name,
      description,
      dailyLimit,
      monthlyLimit,
      allowedIPs,
      expiresAt,
      rateLimit: {
        requestsPerMinute,
        requestsPerHour
      },
      status: 'active'
    });
    
    await apiKeyDoc.save();
    return apiKeyDoc;
  }
  
  /**
   * Revoke an API key
   * @param {string} apiKeyId - API key ID to revoke
   * @returns {Promise<Object>} Updated API key document
   */
  static async revokeApiKey(apiKeyId) {
    const apiKeyDoc = await ApiKeyModel.findOne({ keyId: apiKeyId });
    if (!apiKeyDoc) {
      throw new Error('API key not found');
    }
    
    apiKeyDoc.status = 'revoked';
    apiKeyDoc.updatedAt = new Date();
    
    await apiKeyDoc.save();
    return apiKeyDoc;
  }
  
  /**
   * Suspend an API key
   * @param {string} apiKeyId - API key ID to suspend
   * @returns {Promise<Object>} Updated API key document
   */
  static async suspendApiKey(apiKeyId) {
    const apiKeyDoc = await ApiKeyModel.findOne({ keyId: apiKeyId });
    if (!apiKeyDoc) {
      throw new Error('API key not found');
    }
    
    apiKeyDoc.status = 'suspended';
    apiKeyDoc.updatedAt = new Date();
    
    await apiKeyDoc.save();
    return apiKeyDoc;
  }
  
  /**
   * Reactivate an API key
   * @param {string} apiKeyId - API key ID to reactivate
   * @returns {Promise<Object>} Updated API key document
   */
  static async reactivateApiKey(apiKeyId) {
    const apiKeyDoc = await ApiKeyModel.findOne({ keyId: apiKeyId });
    if (!apiKeyDoc) {
      throw new Error('API key not found');
    }
    
    if (apiKeyDoc.status === 'revoked') {
      throw new Error('Cannot reactivate a revoked API key');
    }
    
    apiKeyDoc.status = 'active';
    apiKeyDoc.updatedAt = new Date();
    
    await apiKeyDoc.save();
    return apiKeyDoc;
  }
  
  /**
   * Update API key limits
   * @param {string} apiKeyId - API key ID
   * @param {Object} limits - New limits
   * @returns {Promise<Object>} Updated API key document
   */
  static async updateApiKeyLimits(apiKeyId, limits) {
    const apiKeyDoc = await ApiKeyModel.findOne({ keyId: apiKeyId });
    if (!apiKeyDoc) {
      throw new Error('API key not found');
    }
    
    if (limits.dailyLimit !== undefined) {
      apiKeyDoc.dailyLimit = limits.dailyLimit;
    }
    
    if (limits.monthlyLimit !== undefined) {
      apiKeyDoc.monthlyLimit = limits.monthlyLimit;
    }
    
    if (limits.requestsPerMinute !== undefined) {
      apiKeyDoc.rateLimit.requestsPerMinute = limits.requestsPerMinute;
    }
    
    if (limits.requestsPerHour !== undefined) {
      apiKeyDoc.rateLimit.requestsPerHour = limits.requestsPerHour;
    }
    
    apiKeyDoc.updatedAt = new Date();
    await apiKeyDoc.save();
    
    return apiKeyDoc;
  }
  
  /**
   * Get API key usage statistics
   * @param {string} apiKeyId - API key ID
   * @param {Date} startDate - Start date for statistics
   * @param {Date} endDate - End date for statistics
   * @returns {Promise<Object>} Usage statistics
   */
  static async getApiKeyUsageStats(apiKeyId, startDate, endDate) {
    const apiKeyDoc = await ApiKeyModel.findOne({ keyId: apiKeyId });
    if (!apiKeyDoc) {
      throw new Error('API key not found');
    }
    
    const stats = await RequestLogModel.getUsageStats(apiKeyId, startDate, endDate);
    const errorStats = await RequestLogModel.getErrorStats(apiKeyId, startDate, endDate);
    
    return {
      apiKey: {
        id: apiKeyDoc.keyId,
        name: apiKeyDoc.name,
        status: apiKeyDoc.status,
        limits: {
          daily: apiKeyDoc.dailyLimit,
          monthly: apiKeyDoc.monthlyLimit,
          rateLimit: apiKeyDoc.rateLimit
        },
        currentUsage: {
          daily: apiKeyDoc.dailyUsage,
          monthly: apiKeyDoc.monthlyUsage,
          total: apiKeyDoc.totalUsage
        }
      },
      periodStats: stats[0] || {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        avgResponseTime: 0,
        totalDataTransferred: 0
      },
      errorBreakdown: errorStats
    };
  }
}

module.exports = ApiKeyGenerator;
