const axios = require('axios');
const https = require('https');

class TelegramHttpClient {
  constructor() {
    this.baseURL = 'https://api.telegram.org';
    this.fileBaseURL = 'https://api.telegram.org/file';
    this.timeout = 30000; // 30 seconds timeout
    
    // Create axios instance with secure defaults
    this.client = axios.create({
      timeout: this.timeout,
      maxRedirects: 5,
      httpsAgent: new https.Agent({
        rejectUnauthorized: true,
        secureProtocol: 'TLSv1_2_method'
      }),
      headers: {
        'User-Agent': 'TelegramProxyService/1.0'
      }
    });
    
    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        config.metadata = { startTime: Date.now() };
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    
    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        response.config.metadata.endTime = Date.now();
        response.config.metadata.duration = response.config.metadata.endTime - response.config.metadata.startTime;
        return response;
      },
      (error) => {
        if (error.config) {
          error.config.metadata = error.config.metadata || {};
          error.config.metadata.endTime = Date.now();
          error.config.metadata.duration = error.config.metadata.endTime - (error.config.metadata.startTime || error.config.metadata.endTime);
        }
        return Promise.reject(error);
      }
    );
  }
  
  /**
   * Proxy request to Telegram Bot API
   * @param {string} method - HTTP method
   * @param {string} path - API path (e.g., '/bot123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11/getMe')
   * @param {Object} data - Request data
   * @param {Object} headers - Request headers
   * @param {Object} params - Query parameters
   * @returns {Promise} Axios response
   */
  async proxyRequest(method, path, data = null, headers = {}, params = {}) {
    try {
      // Determine the base URL based on the path
      let baseURL = this.baseURL;
      if (path.includes('/file/bot')) {
        baseURL = this.fileBaseURL;
        path = path.replace('/file', '');
      }
      
      const url = `${baseURL}${path}`;
      
      // Prepare request config
      const config = {
        method: method.toLowerCase(),
        url: url,
        headers: this.sanitizeHeaders(headers),
        timeout: this.timeout,
        validateStatus: () => true, // Don't throw on any status code
        maxContentLength: 50 * 1024 * 1024, // 50MB max content length
        maxBodyLength: 50 * 1024 * 1024 // 50MB max body length
      };
      
      // Add query parameters
      if (Object.keys(params).length > 0) {
        config.params = params;
      }
      
      // Add request data for POST/PUT/PATCH
      if (data && ['post', 'put', 'patch'].includes(method.toLowerCase())) {
        config.data = data;
      }
      
      // Make the request
      const response = await this.client(config);
      
      return {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data,
        metadata: response.config.metadata
      };
      
    } catch (error) {
      // Handle network errors, timeouts, etc.
      const errorResponse = {
        status: error.response ? error.response.status : 500,
        statusText: error.response ? error.response.statusText : 'Network Error',
        headers: error.response ? error.response.headers : {},
        data: error.response ? error.response.data : {
          ok: false,
          error_code: 500,
          description: error.message || 'Network error occurred'
        },
        metadata: error.config ? error.config.metadata : { duration: 0 },
        isNetworkError: !error.response
      };
      
      return errorResponse;
    }
  }
  
  /**
   * Sanitize headers to remove sensitive information and add security headers
   * @param {Object} headers - Original headers
   * @returns {Object} Sanitized headers
   */
  sanitizeHeaders(headers) {
    const sanitized = { ...headers };
    
    // Remove potentially sensitive headers
    delete sanitized['authorization'];
    delete sanitized['cookie'];
    delete sanitized['x-api-key'];
    delete sanitized['x-auth-token'];
    
    // Remove proxy-specific headers
    delete sanitized['host'];
    delete sanitized['x-forwarded-for'];
    delete sanitized['x-forwarded-proto'];
    delete sanitized['x-real-ip'];
    
    // Ensure content-type is preserved for multipart uploads
    if (headers['content-type']) {
      sanitized['content-type'] = headers['content-type'];
    }
    
    return sanitized;
  }
  
  /**
   * Extract Telegram bot token from path
   * @param {string} path - API path
   * @returns {string|null} Bot token or null if not found
   */
  extractBotToken(path) {
    const match = path.match(/\/bot(\d+:[A-Za-z0-9_-]+)\//);
    return match ? match[1] : null;
  }
  
  /**
   * Extract Telegram method from path
   * @param {string} path - API path
   * @returns {string|null} Telegram method or null if not found
   */
  extractTelegramMethod(path) {
    const match = path.match(/\/bot\d+:[A-Za-z0-9_-]+\/([A-Za-z0-9_]+)/);
    return match ? match[1] : null;
  }
  
  /**
   * Validate if the path is a valid Telegram API path
   * @param {string} path - API path
   * @returns {boolean} True if valid
   */
  isValidTelegramPath(path) {
    // Check for bot API pattern
    const botApiPattern = /^\/bot\d+:[A-Za-z0-9_-]+\/[A-Za-z0-9_]+/;
    // Check for file API pattern
    const fileApiPattern = /^\/file\/bot\d+:[A-Za-z0-9_-]+\//;
    
    return botApiPattern.test(path) || fileApiPattern.test(path);
  }
}

module.exports = new TelegramHttpClient();
