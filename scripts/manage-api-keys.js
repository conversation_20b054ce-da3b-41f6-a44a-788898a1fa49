#!/usr/bin/env node

/**
 * API Key Management Script for Telegram Proxy Service
 * Usage: node scripts/manage-api-keys.js <command> [options]
 */

// Setup global variables like in main app
global._ = require('lodash');
global.config = require('config');
global.Logger = require('../lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.logger = Logger(`${__dirname}/../logs`);

// Load models
require('fs').readdirSync(`${__dirname}/../lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`../lib/models/${file}`);
});

const ApiKeyGenerator = require('../lib/util/apiKeyGenerator');
const mongoConnections = require('../lib/connections/mongo');

async function connectToDatabase() {
  // Wait for MongoDB connection
  return new Promise((resolve, reject) => {
    const connection = mongoConnections('master');
    if (connection.readyState === 1) {
      resolve();
    } else {
      connection.on('connected', resolve);
      connection.on('error', reject);
      setTimeout(() => reject(new Error('Database connection timeout')), 10000);
    }
  });
}

async function createUser(username, email, name) {
  try {
    const existingUser = await UserModel.findOne({ 
      $or: [{ username }, { email }] 
    });
    
    if (existingUser) {
      console.log('❌ User already exists with this username or email');
      return null;
    }
    
    const user = new UserModel({
      username,
      email,
      name,
      password: 'temp_password_change_me', // In production, hash this properly
      status: 1,
      role: 'user'
    });
    
    await user.save();
    console.log('✅ User created successfully:', user._id);
    return user;
  } catch (error) {
    console.error('❌ Error creating user:', error.message);
    return null;
  }
}

async function createApiKey(userId, name, options = {}) {
  try {
    const user = await UserModel.findById(userId);
    if (!user) {
      console.log('❌ User not found');
      return null;
    }
    
    const apiKey = await ApiKeyGenerator.createApiKey(userId, name, options);
    console.log('✅ API Key created successfully:');
    console.log('   Key ID:', apiKey.keyId);
    console.log('   User:', user.username);
    console.log('   Name:', apiKey.name);
    console.log('   Daily Limit:', apiKey.dailyLimit);
    console.log('   Monthly Limit:', apiKey.monthlyLimit);
    console.log('   Status:', apiKey.status);
    
    return apiKey;
  } catch (error) {
    console.error('❌ Error creating API key:', error.message);
    return null;
  }
}

async function listApiKeys(userId = null) {
  try {
    const query = userId ? { userId } : {};
    const apiKeys = await ApiKeyModel.find(query).populate('userId', 'username email name');
    
    if (apiKeys.length === 0) {
      console.log('📝 No API keys found');
      return;
    }
    
    console.log(`📋 Found ${apiKeys.length} API key(s):\n`);
    
    apiKeys.forEach((key, index) => {
      console.log(`${index + 1}. ${key.name}`);
      console.log(`   Key ID: ${key.keyId}`);
      console.log(`   User: ${key.userId.username} (${key.userId.email})`);
      console.log(`   Status: ${key.status}`);
      console.log(`   Usage: ${key.dailyUsage}/${key.dailyLimit} daily, ${key.monthlyUsage}/${key.monthlyLimit} monthly`);
      console.log(`   Total Requests: ${key.totalUsage}`);
      console.log(`   Created: ${key.createdAt}`);
      console.log(`   Last Used: ${key.lastUsedAt || 'Never'}`);
      console.log('');
    });
  } catch (error) {
    console.error('❌ Error listing API keys:', error.message);
  }
}

async function updateApiKeyStatus(keyId, status) {
  try {
    const apiKey = await ApiKeyModel.findOne({ keyId });
    if (!apiKey) {
      console.log('❌ API key not found');
      return;
    }
    
    apiKey.status = status;
    apiKey.updatedAt = new Date();
    await apiKey.save();
    
    console.log(`✅ API key ${keyId} status updated to: ${status}`);
  } catch (error) {
    console.error('❌ Error updating API key status:', error.message);
  }
}

async function deleteApiKey(keyId) {
  try {
    const result = await ApiKeyModel.deleteOne({ keyId });
    if (result.deletedCount === 0) {
      console.log('❌ API key not found');
      return;
    }
    
    console.log(`✅ API key ${keyId} deleted successfully`);
  } catch (error) {
    console.error('❌ Error deleting API key:', error.message);
  }
}

async function getApiKeyStats(keyId) {
  try {
    const apiKey = await ApiKeyModel.findOne({ keyId }).populate('userId', 'username email');
    if (!apiKey) {
      console.log('❌ API key not found');
      return;
    }
    
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000); // Last 30 days
    
    const stats = await ApiKeyGenerator.getApiKeyUsageStats(keyId, startDate, endDate);
    
    console.log(`📊 Statistics for API Key: ${keyId}\n`);
    console.log(`User: ${apiKey.userId.username} (${apiKey.userId.email})`);
    console.log(`Name: ${apiKey.name}`);
    console.log(`Status: ${apiKey.status}\n`);
    
    console.log('📈 Usage Limits:');
    console.log(`   Daily: ${stats.apiKey.currentUsage.daily}/${stats.apiKey.limits.daily}`);
    console.log(`   Monthly: ${stats.apiKey.currentUsage.monthly}/${stats.apiKey.limits.monthly}`);
    console.log(`   Total: ${stats.apiKey.currentUsage.total}\n`);
    
    console.log('📊 Last 30 Days Statistics:');
    console.log(`   Total Requests: ${stats.periodStats.totalRequests}`);
    console.log(`   Successful: ${stats.periodStats.successfulRequests}`);
    console.log(`   Failed: ${stats.periodStats.failedRequests}`);
    console.log(`   Average Response Time: ${Math.round(stats.periodStats.avgResponseTime)}ms`);
    console.log(`   Data Transferred: ${Math.round(stats.periodStats.totalDataTransferred / 1024)}KB\n`);
    
    if (stats.errorBreakdown.length > 0) {
      console.log('❌ Error Breakdown:');
      stats.errorBreakdown.forEach(error => {
        console.log(`   ${error._id}: ${error.count} times`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error getting API key stats:', error.message);
  }
}

function showHelp() {
  console.log(`
🔑 Telegram Proxy Service - API Key Management

Usage: node scripts/manage-api-keys.js <command> [options]

Commands:
  create-user <username> <email> <name>     Create a new user
  create-key <userId> <name> [options]      Create a new API key
  list [userId]                             List all API keys or keys for specific user
  status <keyId> <status>                   Update API key status (active/suspended/revoked)
  delete <keyId>                            Delete an API key
  stats <keyId>                             Show API key usage statistics
  help                                      Show this help message

Examples:
  # Create a user
  node scripts/manage-api-keys.js create-<NAME_EMAIL> "Test User"
  
  # Create an API key
  node scripts/manage-api-keys.js create-key 507f1f77bcf86cd799439011 "My Bot Key"
  
  # List all API keys
  node scripts/manage-api-keys.js list
  
  # Suspend an API key
  node scripts/manage-api-keys.js status tgp_abc123 suspended
  
  # Get statistics
  node scripts/manage-api-keys.js stats tgp_abc123

Options for create-key:
  --daily-limit <number>        Daily request limit (default: 1000)
  --monthly-limit <number>      Monthly request limit (default: 30000)
  --requests-per-minute <number> Rate limit per minute (default: 60)
  --requests-per-hour <number>   Rate limit per hour (default: 1000)
  --description <text>          Description for the API key
`);
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === 'help') {
    showHelp();
    process.exit(0);
  }
  
  try {
    console.log('🔌 Connecting to database...');
    await connectToDatabase();
    console.log('✅ Connected to database\n');
    
    const command = args[0];
    
    switch (command) {
      case 'create-user':
        if (args.length < 4) {
          console.log('❌ Usage: create-user <username> <email> <name>');
          process.exit(1);
        }
        await createUser(args[1], args[2], args[3]);
        break;
        
      case 'create-key':
        if (args.length < 3) {
          console.log('❌ Usage: create-key <userId> <name> [options]');
          process.exit(1);
        }
        
        const options = {};
        for (let i = 3; i < args.length; i += 2) {
          const flag = args[i];
          const value = args[i + 1];
          
          switch (flag) {
            case '--daily-limit':
              options.dailyLimit = parseInt(value);
              break;
            case '--monthly-limit':
              options.monthlyLimit = parseInt(value);
              break;
            case '--requests-per-minute':
              options.requestsPerMinute = parseInt(value);
              break;
            case '--requests-per-hour':
              options.requestsPerHour = parseInt(value);
              break;
            case '--description':
              options.description = value;
              break;
          }
        }
        
        await createApiKey(args[1], args[2], options);
        break;
        
      case 'list':
        await listApiKeys(args[1] || null);
        break;
        
      case 'status':
        if (args.length < 3) {
          console.log('❌ Usage: status <keyId> <status>');
          process.exit(1);
        }
        await updateApiKeyStatus(args[1], args[2]);
        break;
        
      case 'delete':
        if (args.length < 2) {
          console.log('❌ Usage: delete <keyId>');
          process.exit(1);
        }
        await deleteApiKey(args[1]);
        break;
        
      case 'stats':
        if (args.length < 2) {
          console.log('❌ Usage: stats <keyId>');
          process.exit(1);
        }
        await getApiKeyStats(args[1]);
        break;
        
      default:
        console.log(`❌ Unknown command: ${command}`);
        showHelp();
        process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

if (require.main === module) {
  main();
}
