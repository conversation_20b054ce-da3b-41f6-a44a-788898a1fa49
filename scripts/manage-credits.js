#!/usr/bin/env node

/**
 * Credit Management Script
 * 
 * This script provides command-line tools for managing user credits
 * 
 * Usage:
 * node scripts/manage-credits.js <command> [options]
 */

const path = require('path');
const fs = require('fs');

// Setup global variables (same as main app)
global._ = require('lodash');
global.config = require('config');
global.Logger = require('../lib/logger');
global.mongoose = require('mongoose');
global.moment = require('moment');
global.logger = Logger(`${__dirname}/../logs`);

// Load models
fs.readdirSync(`${__dirname}/../lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`../lib/models/${file}`);
});

// Load services
const creditService = require('../lib/services/creditService');

/**
 * Display help information
 */
function showHelp() {
  console.log(`
Credit Management Script

Commands:
  balance <userId>                    Show user credit balance
  history <userId> [limit]            Show user credit history
  add <userId> <amount> <source>      Add credits to user
  deduct <userId> <amount> <reason>   Deduct credits from user
  stats <userId> [days]               Show user credit statistics
  list-users [status]                 List users with credit info
  system-stats [days]                 Show system-wide credit statistics
  config                              Show credit system configuration
  reset-monthly [amount]              Reset monthly credits for all users

Options:
  --help, -h                          Show this help message
  --dry-run                           Show what would be done (for destructive operations)
  --format=<json|table>               Output format (default: table)

Examples:
  node scripts/manage-credits.js balance 507f1f77bcf86cd799439011
  node scripts/manage-credits.js history 507f1f77bcf86cd799439011 20
  node scripts/manage-credits.js add 507f1f77bcf86cd799439011 10000 bonus
  node scripts/manage-credits.js deduct 507f1f77bcf86cd799439011 5000 "Admin adjustment"
  node scripts/manage-credits.js stats 507f1f77bcf86cd799439011 30
  node scripts/manage-credits.js list-users active
  node scripts/manage-credits.js system-stats 7
  node scripts/manage-credits.js reset-monthly 150000 --dry-run
`);
}

/**
 * Format and display user credit balance
 */
async function showUserBalance(userId, format = 'table') {
  try {
    const user = await UserModel.findById(userId);
    if (!user) {
      console.error('❌ User not found');
      return;
    }

    const summary = await creditService.getUserCreditSummary(userId);

    if (format === 'json') {
      console.log(JSON.stringify(summary, null, 2));
      return;
    }

    console.log(`\n💰 Credit Balance for ${user.username} (${user.email})\n`);
    console.log(`📊 Current Balance:`);
    console.log(`   Total Credits: ${summary.credits.total.toLocaleString()}`);
    console.log(`   Monthly Credits: ${summary.credits.monthly.toLocaleString()}`);
    console.log(`   Ad Credits: ${summary.credits.ad.toLocaleString()}`);
    console.log(`\n📈 Lifetime Statistics:`);
    console.log(`   Total Earned: ${summary.credits.totalEarned.toLocaleString()}`);
    console.log(`   Total Used: ${summary.credits.totalUsed.toLocaleString()}`);
    console.log(`\n🎬 Ad Viewing:`);
    console.log(`   Daily Ad Views: ${summary.credits.dailyAdViews}/${summary.limits.dailyAdLimit}`);
    console.log(`   Remaining Today: ${summary.limits.dailyAdViewsRemaining}`);
    
    if (summary.credits.lastAdViewDate) {
      console.log(`   Last Ad View: ${moment(summary.credits.lastAdViewDate).format('YYYY-MM-DD HH:mm:ss')}`);
    }
    
    if (summary.credits.lastMonthlyCreditReset) {
      console.log(`\n📅 Last Monthly Reset: ${moment(summary.credits.lastMonthlyCreditReset).format('YYYY-MM-DD HH:mm:ss')}`);
    }

  } catch (error) {
    console.error('❌ Error getting user balance:', error.message);
  }
}

/**
 * Show user credit history
 */
async function showUserHistory(userId, limit = 20, format = 'table') {
  try {
    const user = await UserModel.findById(userId);
    if (!user) {
      console.error('❌ User not found');
      return;
    }

    const history = await CreditHistoryModel.getUserHistory(userId, { limit });

    if (format === 'json') {
      console.log(JSON.stringify(history, null, 2));
      return;
    }

    console.log(`\n📜 Credit History for ${user.username} (Last ${limit} transactions)\n`);
    
    if (history.length === 0) {
      console.log('No credit transactions found');
      return;
    }

    console.log('Date                 | Type     | Source           | Amount      | Balance After');
    console.log('---------------------|----------|------------------|-------------|------------------');
    
    history.forEach(tx => {
      const date = moment(tx.createdAt).format('YYYY-MM-DD HH:mm:ss');
      const type = tx.type === 'add' ? '+ ADD   ' : '- DEDUCT';
      const source = tx.source.padEnd(16);
      const amount = tx.amount.toLocaleString().padStart(10);
      const balance = tx.balanceAfter.total.toLocaleString().padStart(15);
      
      console.log(`${date} | ${type} | ${source} | ${amount} | ${balance}`);
    });

  } catch (error) {
    console.error('❌ Error getting user history:', error.message);
  }
}

/**
 * Add credits to user
 */
async function addCredits(userId, amount, source, dryRun = false) {
  try {
    const user = await UserModel.findById(userId);
    if (!user) {
      console.error('❌ User not found');
      return;
    }

    amount = parseInt(amount);
    if (isNaN(amount) || amount <= 0) {
      console.error('❌ Invalid amount');
      return;
    }

    if (dryRun) {
      console.log(`\n🔍 DRY RUN - Would add ${amount.toLocaleString()} credits to ${user.username}`);
      console.log(`   Source: ${source}`);
      console.log(`   Current Balance: ${user.getTotalCredits().toLocaleString()}`);
      console.log(`   New Balance Would Be: ${(user.getTotalCredits() + amount).toLocaleString()}`);
      return;
    }

    const result = await creditService.addCredits(userId, amount, source, {
      adminUserId: 'script',
      adminReason: 'Manual credit addition via script'
    });

    console.log(`\n✅ Successfully added ${amount.toLocaleString()} credits to ${user.username}`);
    console.log(`   Transaction ID: ${result.transactionId}`);
    console.log(`   New Balance: ${result.balanceAfter.total.toLocaleString()}`);

  } catch (error) {
    console.error('❌ Error adding credits:', error.message);
  }
}

/**
 * Deduct credits from user
 */
async function deductCredits(userId, amount, reason, dryRun = false) {
  try {
    const user = await UserModel.findById(userId);
    if (!user) {
      console.error('❌ User not found');
      return;
    }

    amount = parseInt(amount);
    if (isNaN(amount) || amount <= 0) {
      console.error('❌ Invalid amount');
      return;
    }

    if (dryRun) {
      console.log(`\n🔍 DRY RUN - Would deduct ${amount.toLocaleString()} credits from ${user.username}`);
      console.log(`   Reason: ${reason}`);
      console.log(`   Current Balance: ${user.getTotalCredits().toLocaleString()}`);
      console.log(`   New Balance Would Be: ${Math.max(0, user.getTotalCredits() - amount).toLocaleString()}`);
      return;
    }

    const result = await creditService.adminAdjustCredits(userId, -amount, reason, 'script');

    console.log(`\n✅ Successfully deducted ${amount.toLocaleString()} credits from ${user.username}`);
    console.log(`   Reason: ${reason}`);
    console.log(`   New Balance: ${result.balanceAfter.total.toLocaleString()}`);

  } catch (error) {
    console.error('❌ Error deducting credits:', error.message);
  }
}

/**
 * Show user credit statistics
 */
async function showUserStats(userId, days = 30, format = 'table') {
  try {
    const user = await UserModel.findById(userId);
    if (!user) {
      console.error('❌ User not found');
      return;
    }

    const summary = await CreditHistoryModel.getUserSummary(userId, days);

    if (format === 'json') {
      console.log(JSON.stringify(summary, null, 2));
      return;
    }

    console.log(`\n📊 Credit Statistics for ${user.username} (Last ${days} days)\n`);
    
    let totalEarned = 0;
    let totalUsed = 0;
    
    console.log('Source               | Earned      | Used        | Net         | Transactions');
    console.log('---------------------|-------------|-------------|-------------|-------------');
    
    summary.forEach(item => {
      const source = item._id.padEnd(20);
      let earned = 0;
      let used = 0;
      let transactions = 0;
      
      item.transactions.forEach(trans => {
        if (trans.type === 'add') {
          earned += trans.amount;
          totalEarned += trans.amount;
        } else {
          used += trans.amount;
          totalUsed += trans.amount;
        }
        transactions += trans.count;
      });
      
      const net = earned - used;
      const earnedStr = earned.toLocaleString().padStart(10);
      const usedStr = used.toLocaleString().padStart(10);
      const netStr = net.toLocaleString().padStart(10);
      const transStr = transactions.toString().padStart(10);
      
      console.log(`${source} | ${earnedStr} | ${usedStr} | ${netStr} | ${transStr}`);
    });
    
    console.log('---------------------|-------------|-------------|-------------|-------------');
    const totalEarnedStr = totalEarned.toLocaleString().padStart(10);
    const totalUsedStr = totalUsed.toLocaleString().padStart(10);
    const totalNetStr = (totalEarned - totalUsed).toLocaleString().padStart(10);
    console.log(`${'TOTAL'.padEnd(20)} | ${totalEarnedStr} | ${totalUsedStr} | ${totalNetStr} |`);

  } catch (error) {
    console.error('❌ Error getting user stats:', error.message);
  }
}

/**
 * List users with credit information
 */
async function listUsers(status = 'all', format = 'table') {
  try {
    const query = {};
    if (status === 'active') query.status = 1;
    if (status === 'inactive') query.status = 0;

    const users = await UserModel.find(query)
      .select('username email status totalCreditBalance monthlyCreditBalance adCreditBalance totalCreditsEarned totalCreditsUsed')
      .limit(50)
      .sort({ totalCreditBalance: -1 });

    if (format === 'json') {
      console.log(JSON.stringify(users, null, 2));
      return;
    }

    console.log(`\n👥 Users with Credit Information (Top 50 by balance)\n`);
    
    console.log('Username             | Email                    | Status | Total    | Monthly  | Ad       | Earned   | Used');
    console.log('---------------------|--------------------------|--------|----------|----------|----------|----------|----------');
    
    users.forEach(user => {
      const username = (user.username || '').padEnd(20);
      const email = (user.email || '').padEnd(25);
      const status = user.status === 1 ? 'Active ' : 'Inactive';
      const total = (user.totalCreditBalance || 0).toLocaleString().padStart(8);
      const monthly = (user.monthlyCreditBalance || 0).toLocaleString().padStart(8);
      const ad = (user.adCreditBalance || 0).toLocaleString().padStart(8);
      const earned = (user.totalCreditsEarned || 0).toLocaleString().padStart(8);
      const used = (user.totalCreditsUsed || 0).toLocaleString().padStart(8);
      
      console.log(`${username} | ${email} | ${status} | ${total} | ${monthly} | ${ad} | ${earned} | ${used}`);
    });

  } catch (error) {
    console.error('❌ Error listing users:', error.message);
  }
}

/**
 * Show system-wide credit statistics
 */
async function showSystemStats(days = 30, format = 'table') {
  try {
    const stats = await CreditHistoryModel.getSystemStats(days);

    if (format === 'json') {
      console.log(JSON.stringify(stats, null, 2));
      return;
    }

    console.log(`\n🌐 System Credit Statistics (Last ${days} days)\n`);
    
    // Group by source and calculate totals
    const summary = {};
    stats.forEach(stat => {
      const source = stat._id.source;
      if (!summary[source]) {
        summary[source] = { earned: 0, used: 0, transactions: 0 };
      }
      
      if (stat._id.type === 'add') {
        summary[source].earned += stat.totalAmount;
      } else {
        summary[source].used += stat.totalAmount;
      }
      summary[source].transactions += stat.count;
    });
    
    console.log('Source               | Earned      | Used        | Net         | Transactions');
    console.log('---------------------|-------------|-------------|-------------|-------------');
    
    let totalEarned = 0;
    let totalUsed = 0;
    let totalTransactions = 0;
    
    Object.entries(summary).forEach(([source, data]) => {
      const sourceStr = source.padEnd(20);
      const earnedStr = data.earned.toLocaleString().padStart(10);
      const usedStr = data.used.toLocaleString().padStart(10);
      const netStr = (data.earned - data.used).toLocaleString().padStart(10);
      const transStr = data.transactions.toString().padStart(10);
      
      console.log(`${sourceStr} | ${earnedStr} | ${usedStr} | ${netStr} | ${transStr}`);
      
      totalEarned += data.earned;
      totalUsed += data.used;
      totalTransactions += data.transactions;
    });
    
    console.log('---------------------|-------------|-------------|-------------|-------------');
    const totalEarnedStr = totalEarned.toLocaleString().padStart(10);
    const totalUsedStr = totalUsed.toLocaleString().padStart(10);
    const totalNetStr = (totalEarned - totalUsed).toLocaleString().padStart(10);
    const totalTransStr = totalTransactions.toString().padStart(10);
    console.log(`${'TOTAL'.padEnd(20)} | ${totalEarnedStr} | ${totalUsedStr} | ${totalNetStr} | ${totalTransStr}`);

  } catch (error) {
    console.error('❌ Error getting system stats:', error.message);
  }
}

/**
 * Show credit system configuration
 */
async function showConfig(format = 'table') {
  try {
    const configs = await SystemConfigModel.getByCategory('credits');
    const adConfigs = await SystemConfigModel.getByCategory('ads');
    const limitConfigs = await SystemConfigModel.getByCategory('limits');

    const allConfigs = [...configs, ...adConfigs, ...limitConfigs];

    if (format === 'json') {
      console.log(JSON.stringify(allConfigs, null, 2));
      return;
    }

    console.log('\n⚙️  Credit System Configuration\n');
    
    console.log('Key                              | Value        | Description');
    console.log('---------------------------------|--------------|------------------------------------------');
    
    allConfigs.forEach(config => {
      const key = config.key.padEnd(32);
      const value = config.value.toString().padStart(11);
      const description = config.description;
      
      console.log(`${key} | ${value} | ${description}`);
    });

  } catch (error) {
    console.error('❌ Error getting configuration:', error.message);
  }
}

/**
 * Main execution function
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
  }

  const command = args[0];
  const dryRun = args.includes('--dry-run');
  const formatArg = args.find(arg => arg.startsWith('--format='));
  const format = formatArg ? formatArg.split('=')[1] : 'table';

  try {
    switch (command) {
      case 'balance':
        if (!args[1]) {
          console.error('❌ User ID required');
          process.exit(1);
        }
        await showUserBalance(args[1], format);
        break;

      case 'history':
        if (!args[1]) {
          console.error('❌ User ID required');
          process.exit(1);
        }
        const limit = args[2] ? parseInt(args[2]) : 20;
        await showUserHistory(args[1], limit, format);
        break;

      case 'add':
        if (!args[1] || !args[2] || !args[3]) {
          console.error('❌ Usage: add <userId> <amount> <source>');
          process.exit(1);
        }
        await addCredits(args[1], args[2], args[3], dryRun);
        break;

      case 'deduct':
        if (!args[1] || !args[2] || !args[3]) {
          console.error('❌ Usage: deduct <userId> <amount> <reason>');
          process.exit(1);
        }
        await deductCredits(args[1], args[2], args[3], dryRun);
        break;

      case 'stats':
        if (!args[1]) {
          console.error('❌ User ID required');
          process.exit(1);
        }
        const days = args[2] ? parseInt(args[2]) : 30;
        await showUserStats(args[1], days, format);
        break;

      case 'list-users':
        const status = args[1] || 'all';
        await listUsers(status, format);
        break;

      case 'system-stats':
        const systemDays = args[1] ? parseInt(args[1]) : 30;
        await showSystemStats(systemDays, format);
        break;

      case 'config':
        await showConfig(format);
        break;

      case 'reset-monthly':
        const amount = args[1] ? parseInt(args[1]) : null;
        if (dryRun) {
          console.log('🔍 DRY RUN - Monthly reset would be performed');
          // Could implement dry run logic here
        } else {
          console.log('⚠️  Use the dedicated monthly-credit-reset.js script for this operation');
        }
        break;

      default:
        console.error(`❌ Unknown command: ${command}`);
        showHelp();
        process.exit(1);
    }

    process.exit(0);

  } catch (error) {
    console.error('❌ Script failed:', error.message);
    logger.logError('Credit management script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  showUserBalance,
  showUserHistory,
  addCredits,
  deductCredits,
  showUserStats,
  listUsers,
  showSystemStats,
  showConfig
};
