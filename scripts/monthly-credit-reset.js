#!/usr/bin/env node

/**
 * Monthly Credit Reset Script
 * 
 * This script runs monthly to reset user credits.
 * Should be scheduled to run on the 1st of each month at 00:00:00
 * 
 * Usage:
 * node scripts/monthly-credit-reset.js [--dry-run] [--amount=100000]
 */

const path = require('path');
const fs = require('fs');

// Setup global variables (same as main app)
global._ = require('lodash');
global.config = require('config');
global.Logger = require('../lib/logger');
global.mongoose = require('mongoose');
global.moment = require('moment');
global.logger = Logger(`${__dirname}/../logs`);

// Load models
fs.readdirSync(`${__dirname}/../lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`../lib/models/${file}`);
});

// Load services
const creditService = require('../lib/services/creditService');

/**
 * Parse command line arguments
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    amount: null,
    help: false
  };

  args.forEach(arg => {
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg.startsWith('--amount=')) {
      options.amount = parseInt(arg.split('=')[1]);
    } else if (arg === '--help' || arg === '-h') {
      options.help = true;
    }
  });

  return options;
}

/**
 * Display help information
 */
function showHelp() {
  console.log(`
Monthly Credit Reset Script

This script resets monthly credits for all active users.

Usage:
  node scripts/monthly-credit-reset.js [options]

Options:
  --dry-run              Show what would be done without making changes
  --amount=<number>      Override default monthly credit amount
  --help, -h             Show this help message

Examples:
  node scripts/monthly-credit-reset.js
  node scripts/monthly-credit-reset.js --dry-run
  node scripts/monthly-credit-reset.js --amount=150000
  node scripts/monthly-credit-reset.js --dry-run --amount=50000

Environment Variables:
  MONTHLY_CREDIT_AMOUNT  Default monthly credit amount (default: 100000)

Notes:
  - This script should be run on the 1st of each month
  - Only active users (status = 1) will receive credits
  - Users who already received credits this month will be skipped
  - All operations are logged for audit purposes
`);
}

/**
 * Validate environment and configuration
 */
async function validateEnvironment() {
  try {
    // Test database connection
    await mongoose.connection.readyState;
    if (mongoose.connection.readyState !== 1) {
      throw new Error('Database not connected');
    }

    // Test that required models exist
    if (!UserModel) {
      throw new Error('UserModel not loaded');
    }

    if (!CreditHistoryModel) {
      throw new Error('CreditHistoryModel not loaded');
    }

    if (!SystemConfigModel) {
      throw new Error('SystemConfigModel not loaded');
    }

    return true;
  } catch (error) {
    logger.logError('Environment validation failed:', error);
    throw error;
  }
}

/**
 * Get users eligible for monthly credit reset
 */
async function getEligibleUsers() {
  try {
    const users = await UserModel.getUsersForMonthlyReset();
    
    logger.logInfo('Found eligible users for monthly reset', {
      count: users.length,
      timestamp: new Date().toISOString()
    });

    return users;
  } catch (error) {
    logger.logError('Error getting eligible users:', error);
    throw error;
  }
}

/**
 * Perform dry run - show what would be done
 */
async function performDryRun(users, creditAmount) {
  console.log('\n🔍 DRY RUN MODE - No changes will be made\n');
  
  console.log(`📊 Monthly Credit Reset Summary:`);
  console.log(`   Credit Amount: ${creditAmount.toLocaleString()} credits`);
  console.log(`   Eligible Users: ${users.length}`);
  console.log(`   Total Credits to Distribute: ${(users.length * creditAmount).toLocaleString()}`);
  
  if (users.length > 0) {
    console.log('\n👥 Users who would receive credits:');
    
    users.slice(0, 10).forEach((user, index) => {
      const lastReset = user.lastMonthlyCreditResetDate 
        ? moment(user.lastMonthlyCreditResetDate).format('YYYY-MM-DD')
        : 'Never';
      
      console.log(`   ${index + 1}. ${user.username} (${user.email})`);
      console.log(`      Current Monthly Credits: ${user.monthlyCreditBalance}`);
      console.log(`      Last Reset: ${lastReset}`);
      console.log(`      Would Receive: ${creditAmount.toLocaleString()} credits`);
      console.log('');
    });
    
    if (users.length > 10) {
      console.log(`   ... and ${users.length - 10} more users`);
    }
  }
  
  console.log('\n✅ Dry run completed. Use without --dry-run to execute.');
}

/**
 * Perform actual monthly credit reset
 */
async function performReset(creditAmount) {
  try {
    console.log('\n🚀 Starting Monthly Credit Reset...\n');
    
    const startTime = Date.now();
    
    // Use credit service to perform reset
    const result = await creditService.resetMonthlyCredits(creditAmount);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('✅ Monthly Credit Reset Completed!\n');
    console.log(`📊 Reset Summary:`);
    console.log(`   Total Users Processed: ${result.totalUsers}`);
    console.log(`   Successful Resets: ${result.successCount}`);
    console.log(`   Failed Resets: ${result.errorCount}`);
    console.log(`   Credit Amount: ${result.creditAmount.toLocaleString()} per user`);
    console.log(`   Total Credits Distributed: ${(result.successCount * result.creditAmount).toLocaleString()}`);
    console.log(`   Processing Time: ${duration}ms`);
    
    if (result.errorCount > 0) {
      console.log('\n❌ Errors occurred during reset:');
      result.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`   ${r.username}: ${r.error}`);
        });
    }
    
    // Log successful resets
    if (result.successCount > 0) {
      console.log('\n✅ Successful resets:');
      result.results
        .filter(r => r.success)
        .slice(0, 5)
        .forEach(r => {
          console.log(`   ${r.username}: ${r.oldBalance} → ${r.newBalance} credits`);
        });
      
      if (result.successCount > 5) {
        console.log(`   ... and ${result.successCount - 5} more users`);
      }
    }
    
    return result;
    
  } catch (error) {
    logger.logError('Monthly credit reset failed:', error);
    console.error('\n❌ Monthly Credit Reset Failed:', error.message);
    throw error;
  }
}

/**
 * Main execution function
 */
async function main() {
  const options = parseArgs();
  
  if (options.help) {
    showHelp();
    process.exit(0);
  }
  
  try {
    console.log('🔄 Monthly Credit Reset Script Starting...');
    console.log(`   Timestamp: ${new Date().toISOString()}`);
    console.log(`   Mode: ${options.dryRun ? 'DRY RUN' : 'LIVE'}`);
    
    // Validate environment
    await validateEnvironment();
    console.log('✅ Environment validation passed');
    
    // Get credit amount
    const creditAmount = options.amount || 
                        await SystemConfigModel.getCachedValue('monthly_credit_amount', 100000);
    
    console.log(`💰 Monthly Credit Amount: ${creditAmount.toLocaleString()}`);
    
    // Get eligible users
    const users = await getEligibleUsers();
    
    if (users.length === 0) {
      console.log('ℹ️  No users eligible for monthly credit reset');
      process.exit(0);
    }
    
    if (options.dryRun) {
      await performDryRun(users, creditAmount);
    } else {
      const result = await performReset(creditAmount);
      
      // Log final summary
      logger.logInfo('Monthly credit reset completed', {
        totalUsers: result.totalUsers,
        successCount: result.successCount,
        errorCount: result.errorCount,
        creditAmount: result.creditAmount,
        timestamp: new Date().toISOString()
      });
    }
    
    console.log('\n🎉 Script completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('\n💥 Script failed:', error.message);
    logger.logError('Monthly credit reset script failed:', error);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n⚠️  Script interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Script terminated');
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  main,
  performDryRun,
  performReset,
  getEligibleUsers
};
