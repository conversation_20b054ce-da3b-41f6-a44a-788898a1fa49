#!/usr/bin/env node

/**
 * API Key Rate Limiting Test
 */

const axios = require('axios');

const PROXY_BASE_URL = 'http://localhost:3009';
const TEST_API_KEY = 'tgp_gZOixCEGIhysLomAC7Bla9RKBnZC0o1M';
const BOT_TOKEN = '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11';

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function makeRequestWithApiKey() {
  try {
    const response = await axios.get(
      `${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/getMe`,
      { 
        headers: {
          'X-API-Key': TEST_API_KEY
        },
        timeout: 10000 // Longer timeout for Telegram API
      }
    );
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response ? error.response.status : 0,
      data: error.response ? error.response.data : { error: error.message }
    };
  }
}

async function testApiKeyRateLimit() {
  console.log('🔑 Testing API Key Rate Limiting\n');
  console.log('Note: API key rate limit is 60 requests per minute');
  console.log('We will send requests and look for rate limiting...\n');
  
  let successCount = 0;
  let rateLimitCount = 0;
  let errorCount = 0;
  let telegramErrorCount = 0;
  
  // Send requests one by one to avoid overwhelming
  for (let i = 1; i <= 10; i++) {
    console.log(`Sending request ${i}...`);
    
    const result = await makeRequestWithApiKey();
    
    if (result.status === 429) {
      rateLimitCount++;
      console.log(`❌ Request ${i}: Rate limited!`);
      console.log('   Response:', result.data);
      break;
    } else if (result.status === 500) {
      telegramErrorCount++;
      console.log(`✅ Request ${i}: Proxy worked, Telegram error (expected with fake token)`);
    } else if (result.status === 200) {
      successCount++;
      console.log(`✅ Request ${i}: Success`);
    } else if (result.status === 0) {
      errorCount++;
      console.log(`⚠️  Request ${i}: Network/timeout error`);
    } else {
      errorCount++;
      console.log(`⚠️  Request ${i}: Unexpected status ${result.status}`);
    }
    
    // Small delay between requests
    await sleep(100);
  }
  
  console.log('\n📊 Results:');
  console.log(`   Successful proxy requests: ${successCount + telegramErrorCount}`);
  console.log(`   Rate limited: ${rateLimitCount}`);
  console.log(`   Network errors: ${errorCount}`);
  
  if (rateLimitCount > 0) {
    console.log('\n✅ API key rate limiting is working!');
  } else {
    console.log('\n⚠️  No rate limiting detected in this test');
    console.log('   This could be normal if we didn\'t hit the limit');
  }
}

async function testRapidRequests() {
  console.log('\n🚀 Testing Rapid API Key Requests\n');
  console.log('Sending 20 rapid requests to trigger rate limiting...\n');
  
  const promises = [];
  
  // Send multiple concurrent requests
  for (let i = 1; i <= 20; i++) {
    promises.push(
      makeRequestWithApiKey().then(result => ({
        requestId: i,
        ...result
      }))
    );
  }
  
  const results = await Promise.all(promises);
  
  // Analyze results
  let successCount = 0;
  let rateLimitCount = 0;
  let errorCount = 0;
  
  results.forEach(result => {
    if (result.status === 429) {
      rateLimitCount++;
      console.log(`❌ Request ${result.requestId}: Rate limited`);
    } else if (result.status === 500 || result.status === 200) {
      successCount++;
      console.log(`✅ Request ${result.requestId}: Proxy worked (status: ${result.status})`);
    } else {
      errorCount++;
      console.log(`⚠️  Request ${result.requestId}: Error (status: ${result.status})`);
    }
  });
  
  console.log('\n📊 Rapid Request Results:');
  console.log(`   Total requests: ${results.length}`);
  console.log(`   Successful: ${successCount}`);
  console.log(`   Rate limited: ${rateLimitCount}`);
  console.log(`   Errors: ${errorCount}`);
  
  if (rateLimitCount > 0) {
    console.log('\n✅ Rate limiting works under concurrent load!');
  } else {
    console.log('\n⚠️  No rate limiting detected under concurrent load');
  }
}

async function main() {
  console.log('🧪 API Key Rate Limiting Test\n');
  
  try {
    // Test health first
    console.log('🏥 Checking service health...');
    const healthResult = await axios.get(`${PROXY_BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ Service is running\n');
    
    // Test sequential requests
    await testApiKeyRateLimit();
    
    // Wait a bit
    console.log('\nWaiting 3 seconds before rapid test...');
    await sleep(3000);
    
    // Test concurrent requests
    await testRapidRequests();
    
    console.log('\n🏁 API Key rate limiting tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

main();
