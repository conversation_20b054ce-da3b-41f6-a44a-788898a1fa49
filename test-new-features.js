#!/usr/bin/env node

/**
 * Test script for new features:
 * - Webhook management
 * - Access control
 * - Monitoring & alerting
 */

const axios = require('axios');

const PROXY_BASE_URL = 'http://localhost:3009';
const TEST_API_KEY = 'tgp_xGQBzBi7ULzKmcbymCfKne80dAHFaHyF';
const BOT_TOKEN = '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11';

async function testMonitoringEndpoints() {
  console.log('📊 Testing Monitoring Endpoints...\n');

  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${PROXY_BASE_URL}/health`);
    console.log('✅ Health check:', healthResponse.data.status);
    console.log('   Checks:', Object.keys(healthResponse.data.checks || {}));

    // Test metrics endpoint
    console.log('\n2. Testing metrics endpoint...');
    const metricsResponse = await axios.get(`${PROXY_BASE_URL}/metrics`);
    console.log('✅ Metrics retrieved');
    console.log('   System CPU:', metricsResponse.data.system?.cpuUsage?.toFixed(2) + '%');
    console.log('   Memory usage:', metricsResponse.data.system?.memoryUsage?.toFixed(2) + '%');
    console.log('   Total requests:', metricsResponse.data.application?.requestsTotal);

    // Test Prometheus metrics
    console.log('\n3. Testing Prometheus metrics...');
    const prometheusResponse = await axios.get(`${PROXY_BASE_URL}/metrics/prometheus`);
    console.log('✅ Prometheus metrics retrieved');
    console.log('   Format: text/plain');
    console.log('   Lines:', prometheusResponse.data.split('\n').length);

    // Test alerts endpoints
    console.log('\n4. Testing alerts endpoints...');
    const alertsResponse = await axios.get(`${PROXY_BASE_URL}/alerts`);
    console.log('✅ Active alerts:', alertsResponse.data.alerts?.length || 0);

    const alertStatsResponse = await axios.get(`${PROXY_BASE_URL}/alerts/stats`);
    console.log('✅ Alert stats retrieved');
    console.log('   Active alerts:', alertStatsResponse.data.activeAlerts);
    console.log('   Alerts last 24h:', alertStatsResponse.data.alertsLast24h);

    console.log('\n✅ All monitoring endpoints working!');

  } catch (error) {
    console.error('❌ Monitoring test error:', error.message);
  }
}

async function testWebhookManagement() {
  console.log('\n🔗 Testing Webhook Management...\n');

  try {
    // Test webhook setup
    console.log('1. Testing webhook setup...');
    const setupResponse = await axios.post(`${PROXY_BASE_URL}/api/v1.0/webhook/setup`, {
      botToken: BOT_TOKEN,
      targetUrl: 'https://example.com/webhook',
      secretToken: 'test_secret_123',
      maxConnections: 10,
      allowedUpdates: ['message', 'callback_query']
    }, {
      headers: { 'X-API-Key': TEST_API_KEY }
    });

    if (setupResponse.status === 200) {
      console.log('✅ Webhook setup successful');
      console.log('   Webhook ID:', setupResponse.data.data?.webhookId);
      console.log('   Proxy URL:', setupResponse.data.data?.proxyUrl);
    } else {
      console.log('⚠️  Webhook setup response:', setupResponse.status);
    }

    // Test webhook list
    console.log('\n2. Testing webhook list...');
    const listResponse = await axios.get(`${PROXY_BASE_URL}/api/v1.0/webhook/list`, {
      headers: { 'X-API-Key': TEST_API_KEY }
    });

    console.log('✅ Webhook list retrieved');
    console.log('   Total webhooks:', listResponse.data.data?.totalWebhooks);
    console.log('   Active webhooks:', listResponse.data.data?.activeWebhooks);

    // Test webhook info
    console.log('\n3. Testing webhook info...');
    const infoResponse = await axios.get(`${PROXY_BASE_URL}/api/v1.0/webhook/info/${BOT_TOKEN}`, {
      headers: { 'X-API-Key': TEST_API_KEY }
    });

    if (infoResponse.status === 200) {
      console.log('✅ Webhook info retrieved');
      console.log('   Status:', infoResponse.data.data?.status);
      console.log('   Health:', infoResponse.data.data?.healthStatus);
    }

    // Test webhook removal
    console.log('\n4. Testing webhook removal...');
    const removeResponse = await axios.post(`${PROXY_BASE_URL}/api/v1.0/webhook/remove`, {
      botToken: BOT_TOKEN
    }, {
      headers: { 'X-API-Key': TEST_API_KEY }
    });

    if (removeResponse.status === 200) {
      console.log('✅ Webhook removed successfully');
    }

    console.log('\n✅ Webhook management tests completed!');

  } catch (error) {
    if (error.response) {
      console.error('❌ Webhook test error:', error.response.status, error.response.data);
    } else {
      console.error('❌ Webhook test error:', error.message);
    }
  }
}

async function testAccessControl() {
  console.log('\n🛡️  Testing Access Control...\n');

  try {
    // Test request with valid API key (should work)
    console.log('1. Testing normal request (should work)...');
    const normalResponse = await axios.get(`${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/getMe`, {
      headers: { 'X-API-Key': TEST_API_KEY },
      timeout: 5000
    });

    console.log('✅ Normal request status:', normalResponse.status);

    // Test request without API key (should be blocked by auth)
    console.log('\n2. Testing request without API key (should be blocked)...');
    try {
      await axios.get(`${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/getMe`, {
        timeout: 3000
      });
      console.log('❌ Request should have been blocked');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Request correctly blocked by authentication');
      } else {
        console.log('⚠️  Unexpected error:', error.message);
      }
    }

    console.log('\n✅ Access control tests completed!');

  } catch (error) {
    if (error.response) {
      console.error('❌ Access control test error:', error.response.status, error.response.data);
    } else {
      console.error('❌ Access control test error:', error.message);
    }
  }
}

async function testAlertSystem() {
  console.log('\n🚨 Testing Alert System...\n');

  try {
    // Trigger test alert
    console.log('1. Triggering test alert...');
    const testAlertResponse = await axios.post(`${PROXY_BASE_URL}/alerts/test`);
    console.log('✅ Test alert triggered:', testAlertResponse.data.message);

    // Wait a moment for alert to be processed
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check active alerts
    console.log('\n2. Checking active alerts...');
    const alertsResponse = await axios.get(`${PROXY_BASE_URL}/alerts`);
    console.log('✅ Active alerts:', alertsResponse.data.alerts?.length || 0);

    if (alertsResponse.data.alerts?.length > 0) {
      const testAlert = alertsResponse.data.alerts.find(alert => alert.key === 'test_alert');
      if (testAlert) {
        console.log('   Test alert found:', testAlert.severity, '-', testAlert.message);
      }
    }

    // Check alert history
    console.log('\n3. Checking alert history...');
    const historyResponse = await axios.get(`${PROXY_BASE_URL}/alerts/history?limit=5`);
    console.log('✅ Alert history entries:', historyResponse.data.history?.length || 0);

    console.log('\n✅ Alert system tests completed!');

  } catch (error) {
    console.error('❌ Alert system test error:', error.message);
  }
}

async function generateSomeLoad() {
  console.log('\n⚡ Generating some load for metrics...\n');

  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(
      axios.get(`${PROXY_BASE_URL}/health`).catch(() => {})
    );
  }

  await Promise.all(promises);
  console.log('✅ Generated some requests for metrics');
}

async function main() {
  console.log('🧪 Testing New Features - Telegram Proxy Service\n');
  console.log('Testing: Webhooks, Access Control, Monitoring & Alerting\n');

  try {
    // Check if service is running
    console.log('🏥 Checking service health...');
    await axios.get(`${PROXY_BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ Service is running\n');

    // Generate some load first
    await generateSomeLoad();

    // Test monitoring endpoints
    await testMonitoringEndpoints();

    // Test webhook management
    await testWebhookManagement();

    // Test access control
    await testAccessControl();

    // Test alert system
    await testAlertSystem();

    console.log('\n🎉 All new feature tests completed!');
    console.log('\n📋 Summary of new features tested:');
    console.log('   ✅ Health & Metrics endpoints');
    console.log('   ✅ Prometheus metrics export');
    console.log('   ✅ Webhook management (setup/remove/list/info)');
    console.log('   ✅ Access control middleware');
    console.log('   ✅ Alert system (trigger/list/history)');

  } catch (error) {
    console.error('❌ Service is not running or test failed:', error.message);
    console.log('\nMake sure the proxy service is running on port 3009');
    process.exit(1);
  }
}

main();
