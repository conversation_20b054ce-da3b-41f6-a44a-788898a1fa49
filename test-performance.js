#!/usr/bin/env node

/**
 * Performance Testing for Telegram Proxy Service
 */

const axios = require('axios');

const PROXY_BASE_URL = 'http://localhost:3009';
const TEST_API_KEY = 'tgp_gZOixCEGIhysLomAC7Bla9RKBnZC0o1M';
const BOT_TOKEN = '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11';

async function makeRequest() {
  const startTime = Date.now();
  
  try {
    const response = await axios.get(
      `${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/getMe`,
      {
        headers: { 'X-API-Key': TEST_API_KEY },
        timeout: 3000
      }
    );
    
    const endTime = Date.now();
    return {
      success: true,
      responseTime: endTime - startTime,
      status: response.status,
      size: JSON.stringify(response.data).length
    };
  } catch (error) {
    const endTime = Date.now();
    return {
      success: false,
      responseTime: endTime - startTime,
      status: error.response ? error.response.status : 0,
      error: error.message,
      size: 0
    };
  }
}

async function testSequentialPerformance() {
  console.log('📈 Sequential Performance Test\n');
  console.log('Sending 20 sequential requests...\n');
  
  const results = [];
  const startTime = Date.now();
  
  for (let i = 1; i <= 20; i++) {
    process.stdout.write(`\rRequest ${i}/20...`);
    const result = await makeRequest();
    results.push(result);
  }
  
  const totalTime = Date.now() - startTime;
  console.log('\n');
  
  // Analyze results
  const successful = results.filter(r => r.success || r.status === 500);
  const failed = results.filter(r => !r.success && r.status !== 500);
  const responseTimes = successful.map(r => r.responseTime);
  
  const avgResponseTime = responseTimes.length > 0 ? 
    responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;
  const minResponseTime = responseTimes.length > 0 ? Math.min(...responseTimes) : 0;
  const maxResponseTime = responseTimes.length > 0 ? Math.max(...responseTimes) : 0;
  
  console.log('📊 Sequential Performance Results:');
  console.log(`   Total requests: ${results.length}`);
  console.log(`   Successful: ${successful.length}`);
  console.log(`   Failed: ${failed.length}`);
  console.log(`   Total time: ${totalTime}ms`);
  console.log(`   Requests per second: ${(results.length / (totalTime / 1000)).toFixed(2)}`);
  console.log(`   Average response time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`   Min response time: ${minResponseTime}ms`);
  console.log(`   Max response time: ${maxResponseTime}ms`);
  
  return { successful: successful.length, failed: failed.length, avgResponseTime };
}

async function testConcurrentPerformance() {
  console.log('\n⚡ Concurrent Performance Test\n');
  console.log('Sending 10 concurrent requests...\n');
  
  const startTime = Date.now();
  
  // Create 10 concurrent requests
  const promises = Array.from({ length: 10 }, (_, i) => 
    makeRequest().then(result => ({ ...result, requestId: i + 1 }))
  );
  
  const results = await Promise.all(promises);
  const totalTime = Date.now() - startTime;
  
  // Analyze results
  const successful = results.filter(r => r.success || r.status === 500);
  const failed = results.filter(r => !r.success && r.status !== 500);
  const responseTimes = successful.map(r => r.responseTime);
  
  const avgResponseTime = responseTimes.length > 0 ? 
    responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;
  const minResponseTime = responseTimes.length > 0 ? Math.min(...responseTimes) : 0;
  const maxResponseTime = responseTimes.length > 0 ? Math.max(...responseTimes) : 0;
  
  console.log('📊 Concurrent Performance Results:');
  console.log(`   Total requests: ${results.length}`);
  console.log(`   Successful: ${successful.length}`);
  console.log(`   Failed: ${failed.length}`);
  console.log(`   Total time: ${totalTime}ms`);
  console.log(`   Requests per second: ${(results.length / (totalTime / 1000)).toFixed(2)}`);
  console.log(`   Average response time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`   Min response time: ${minResponseTime}ms`);
  console.log(`   Max response time: ${maxResponseTime}ms`);
  
  // Show individual results
  console.log('\n📋 Individual Request Results:');
  results.forEach(result => {
    const status = result.success ? '✅' : (result.status === 500 ? '⚠️' : '❌');
    console.log(`   ${status} Request ${result.requestId}: ${result.responseTime}ms (status: ${result.status})`);
  });
  
  return { successful: successful.length, failed: failed.length, avgResponseTime };
}

async function testHealthEndpointPerformance() {
  console.log('\n🏥 Health Endpoint Performance Test\n');
  console.log('Testing health endpoint performance...\n');
  
  const results = [];
  const startTime = Date.now();
  
  for (let i = 1; i <= 10; i++) {
    const reqStartTime = Date.now();
    try {
      const response = await axios.get(`${PROXY_BASE_URL}/health`, { timeout: 1000 });
      const responseTime = Date.now() - reqStartTime;
      results.push({ success: true, responseTime, status: response.status });
    } catch (error) {
      const responseTime = Date.now() - reqStartTime;
      results.push({ success: false, responseTime, status: 0 });
    }
  }
  
  const totalTime = Date.now() - startTime;
  const successful = results.filter(r => r.success);
  const avgResponseTime = successful.length > 0 ? 
    successful.reduce((sum, r) => sum + r.responseTime, 0) / successful.length : 0;
  
  console.log('📊 Health Endpoint Results:');
  console.log(`   Successful: ${successful.length}/10`);
  console.log(`   Average response time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`   Total time: ${totalTime}ms`);
  
  return avgResponseTime;
}

async function main() {
  console.log('🚀 Telegram Proxy Service - Performance Testing\n');
  
  try {
    // Test health endpoint first
    const healthTime = await testHealthEndpointPerformance();
    
    // Test sequential performance
    const seqResults = await testSequentialPerformance();
    
    // Test concurrent performance
    const concResults = await testConcurrentPerformance();
    
    // Summary
    console.log('\n🎯 Performance Summary:');
    console.log(`   Health endpoint avg: ${healthTime.toFixed(2)}ms`);
    console.log(`   Sequential avg: ${seqResults.avgResponseTime.toFixed(2)}ms`);
    console.log(`   Concurrent avg: ${concResults.avgResponseTime.toFixed(2)}ms`);
    
    // Performance evaluation
    console.log('\n📈 Performance Evaluation:');
    if (healthTime < 10) {
      console.log('   ✅ Health endpoint: Excellent (<10ms)');
    } else if (healthTime < 50) {
      console.log('   ✅ Health endpoint: Good (<50ms)');
    } else {
      console.log('   ⚠️  Health endpoint: Needs optimization (>50ms)');
    }
    
    if (seqResults.avgResponseTime < 100) {
      console.log('   ✅ Sequential requests: Excellent (<100ms)');
    } else if (seqResults.avgResponseTime < 500) {
      console.log('   ✅ Sequential requests: Good (<500ms)');
    } else {
      console.log('   ⚠️  Sequential requests: Needs optimization (>500ms)');
    }
    
    if (concResults.avgResponseTime < 200) {
      console.log('   ✅ Concurrent requests: Excellent (<200ms)');
    } else if (concResults.avgResponseTime < 1000) {
      console.log('   ✅ Concurrent requests: Good (<1000ms)');
    } else {
      console.log('   ⚠️  Concurrent requests: Needs optimization (>1000ms)');
    }
    
    console.log('\n🏁 Performance testing completed!');
    
  } catch (error) {
    console.error('❌ Performance test failed:', error.message);
    process.exit(1);
  }
}

main();
