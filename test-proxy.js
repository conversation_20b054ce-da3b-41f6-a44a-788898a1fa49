/**
 * Simple test script for Telegram Proxy Service
 * This script demonstrates how to use the proxy service
 */

const axios = require('axios');

// Configuration
const PROXY_BASE_URL = 'http://localhost:3009';
const TEST_API_KEY = 'tgp_gZOixCEGIhysLomAC7Bla9RKBnZC0o1M'; // Real API key from database
const BOT_TOKEN = '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11'; // Example bot token

async function testProxyService() {
  console.log('🚀 Testing Telegram Proxy Service...\n');

  // Test 1: Health Check
  console.log('1. Testing health check...');
  try {
    const response = await axios.get(`${PROXY_BASE_URL}/health`);
    console.log('✅ Health check passed:', response.data);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Proxy Request without API Key (should fail)
  console.log('2. Testing proxy request without API key (should fail)...');
  try {
    const response = await axios.get(`${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/getMe`);
    console.log('❌ Request should have failed but succeeded:', response.data);
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly rejected request without API key:', error.response.data);
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Proxy Request with Invalid API Key (should fail)
  console.log('3. Testing proxy request with invalid API key (should fail)...');
  try {
    const response = await axios.get(`${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/getMe`, {
      headers: {
        'X-API-Key': 'invalid_key_123'
      }
    });
    console.log('❌ Request should have failed but succeeded:', response.data);
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly rejected request with invalid API key:', error.response.data);
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 4: Proxy Request with Valid API Key (will fail at Telegram level due to fake token)
  console.log('4. Testing proxy request with valid API key...');
  try {
    const response = await axios.get(`${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/getMe`, {
      headers: {
        'X-API-Key': TEST_API_KEY
      }
    });
    console.log('Response from Telegram (via proxy):', response.data);
  } catch (error) {
    if (error.response) {
      console.log('Response status:', error.response.status);
      console.log('Response data:', error.response.data);

      // If we get a response from Telegram (even an error), it means our proxy is working
      if (error.response.data && error.response.data.hasOwnProperty('ok')) {
        console.log('✅ Proxy is working! Got response from Telegram API (error expected due to fake token)');
      }
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 5: Test Alternative Route Pattern
  console.log('5. Testing alternative route pattern...');
  try {
    const response = await axios.get(`${PROXY_BASE_URL}/api/v1.0/bot${BOT_TOKEN}/getMe`, {
      headers: {
        'X-API-Key': TEST_API_KEY
      }
    });
    console.log('Response from alternative route:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('Alternative route status:', error.response.status);
      console.log('Alternative route data:', error.response.data);

      if (error.response.data && error.response.data.hasOwnProperty('ok')) {
        console.log('✅ Alternative route is working!');
      }
    } else {
      console.log('❌ Alternative route network error:', error.message);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 6: Test POST Request
  console.log('6. Testing POST request...');
  try {
    const response = await axios.post(`${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/sendMessage`, {
      chat_id: '123456789',
      text: 'Hello from proxy!'
    }, {
      headers: {
        'X-API-Key': TEST_API_KEY,
        'Content-Type': 'application/json'
      }
    });
    console.log('POST response:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('POST status:', error.response.status);
      console.log('POST data:', error.response.data);

      if (error.response.data && error.response.data.hasOwnProperty('ok')) {
        console.log('✅ POST proxy is working!');
      }
    } else {
      console.log('❌ POST network error:', error.message);
    }
  }

  console.log('\n🏁 Test completed!');
}

// Helper function to create a test API key
async function createTestApiKey() {
  console.log('📝 Instructions to create a test API key:\n');
  console.log('1. Start your MongoDB server');
  console.log('2. Connect to your database');
  console.log('3. Create a test user:');
  console.log(`   db.users.insertOne({
     username: "testuser",
     email: "<EMAIL>",
     name: "Test User",
     password: "hashedpassword",
     status: 1,
     createdAt: ${Date.now()},
     updatedAt: ${Date.now()}
   })`);
  console.log('\n4. Create a test API key:');
  console.log(`   db.apikeys.insertOne({
     keyId: "${TEST_API_KEY}",
     userId: ObjectId("YOUR_USER_ID_HERE"),
     name: "Test API Key",
     description: "Test key for proxy service",
     dailyLimit: 1000,
     monthlyLimit: 30000,
     dailyUsage: 0,
     monthlyUsage: 0,
     totalUsage: 0,
     lastDailyReset: new Date(),
     lastMonthlyReset: new Date(),
     status: "active",
     rateLimit: {
       requestsPerMinute: 60,
       requestsPerHour: 1000
     },
     createdAt: new Date(),
     updatedAt: new Date()
   })`);
  console.log('\n5. Then run this test script again\n');
}

// Main execution
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    console.log('Usage: node test-proxy.js [--create-key]');
    console.log('  --create-key: Show instructions to create a test API key');
    process.exit(0);
  }

  if (args.includes('--create-key')) {
    createTestApiKey();
  } else {
    testProxyService().catch(console.error);
  }
}

module.exports = { testProxyService, createTestApiKey };
