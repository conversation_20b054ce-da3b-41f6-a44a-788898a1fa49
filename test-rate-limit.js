#!/usr/bin/env node

/**
 * Rate Limiting Test Script for Telegram Proxy Service
 * Tests both global IP rate limiting and API key rate limiting
 */

const axios = require('axios');

// Configuration
const PROXY_BASE_URL = 'http://localhost:3009';
const TEST_API_KEY = 'tgp_gZOixCEGIhysLomAC7Bla9RKBnZC0o1M';
const BOT_TOKEN = '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11';

// Test configuration
const GLOBAL_RATE_LIMIT = 100; // requests per minute per IP
const API_KEY_RATE_LIMIT = 60;  // requests per minute per API key

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function makeRequest(useApiKey = true, timeout = 5000) {
  const headers = {};
  if (useApiKey) {
    headers['X-API-Key'] = TEST_API_KEY;
  }
  
  try {
    const response = await axios.get(
      `${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/getMe`,
      { 
        headers,
        timeout
      }
    );
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response ? error.response.status : 0,
      data: error.response ? error.response.data : { error: error.message }
    };
  }
}

async function testGlobalRateLimit() {
  console.log('🌍 Testing Global Rate Limit (per IP)...\n');
  console.log(`Target: ${GLOBAL_RATE_LIMIT} requests per minute per IP`);
  console.log('Strategy: Send requests rapidly without API key\n');
  
  let successCount = 0;
  let rateLimitCount = 0;
  let otherErrorCount = 0;
  
  console.log('Sending requests...');
  
  for (let i = 1; i <= GLOBAL_RATE_LIMIT + 20; i++) {
    const result = await makeRequest(false, 2000); // No API key, short timeout
    
    if (result.status === 429) {
      rateLimitCount++;
      console.log(`❌ Request ${i}: Rate limited (429)`);
      if (rateLimitCount === 1) {
        console.log(`   First rate limit hit at request ${i}`);
        console.log(`   Response:`, result.data);
      }
      break; // Stop after first rate limit
    } else if (result.status === 401) {
      successCount++; // Expected - no API key
      if (i % 10 === 0) {
        console.log(`✅ Request ${i}: Expected 401 (no API key)`);
      }
    } else {
      otherErrorCount++;
      console.log(`⚠️  Request ${i}: Unexpected status ${result.status}`);
    }
    
    // Small delay to avoid overwhelming
    await sleep(10);
  }
  
  console.log('\n📊 Global Rate Limit Test Results:');
  console.log(`   Successful requests (401): ${successCount}`);
  console.log(`   Rate limited (429): ${rateLimitCount}`);
  console.log(`   Other errors: ${otherErrorCount}`);
  
  if (rateLimitCount > 0) {
    console.log('✅ Global rate limiting is working!');
  } else {
    console.log('❌ Global rate limiting may not be working properly');
  }
  
  console.log('\n' + '='.repeat(60) + '\n');
}

async function testApiKeyRateLimit() {
  console.log('🔑 Testing API Key Rate Limit...\n');
  console.log(`Target: ${API_KEY_RATE_LIMIT} requests per minute per API key`);
  console.log('Strategy: Send requests rapidly with valid API key\n');
  
  let successCount = 0;
  let rateLimitCount = 0;
  let otherErrorCount = 0;
  let telegramErrorCount = 0;
  
  console.log('Sending requests with API key...');
  
  for (let i = 1; i <= API_KEY_RATE_LIMIT + 10; i++) {
    const result = await makeRequest(true, 3000); // With API key
    
    if (result.status === 429) {
      rateLimitCount++;
      console.log(`❌ Request ${i}: Rate limited (429)`);
      if (rateLimitCount === 1) {
        console.log(`   First rate limit hit at request ${i}`);
        console.log(`   Response:`, result.data);
      }
      break; // Stop after first rate limit
    } else if (result.status === 500 && result.data.error_code === 500) {
      telegramErrorCount++; // Expected - fake bot token
      if (i % 10 === 0) {
        console.log(`✅ Request ${i}: Expected Telegram error (fake token)`);
      }
    } else if (result.status === 200 || result.status === 500) {
      successCount++; // Proxy worked, even if Telegram returned error
      if (i % 10 === 0) {
        console.log(`✅ Request ${i}: Proxy successful (status: ${result.status})`);
      }
    } else {
      otherErrorCount++;
      console.log(`⚠️  Request ${i}: Unexpected status ${result.status}`);
      console.log(`   Response:`, result.data);
    }
    
    // Small delay to avoid overwhelming
    await sleep(50);
  }
  
  console.log('\n📊 API Key Rate Limit Test Results:');
  console.log(`   Successful proxy requests: ${successCount + telegramErrorCount}`);
  console.log(`   Rate limited (429): ${rateLimitCount}`);
  console.log(`   Other errors: ${otherErrorCount}`);
  
  if (rateLimitCount > 0) {
    console.log('✅ API key rate limiting is working!');
  } else {
    console.log('❌ API key rate limiting may not be working properly');
  }
  
  console.log('\n' + '='.repeat(60) + '\n');
}

async function testRateLimitRecovery() {
  console.log('🔄 Testing Rate Limit Recovery...\n');
  
  // First, trigger rate limit
  console.log('Step 1: Triggering rate limit...');
  let rateLimited = false;
  
  for (let i = 1; i <= 10; i++) {
    const result = await makeRequest(true, 2000);
    if (result.status === 429) {
      console.log(`✅ Rate limit triggered at request ${i}`);
      console.log(`   Retry after: ${result.data.retryAfter} seconds`);
      rateLimited = true;
      break;
    }
    await sleep(100);
  }
  
  if (!rateLimited) {
    console.log('❌ Could not trigger rate limit for recovery test');
    return;
  }
  
  // Wait for recovery
  console.log('\nStep 2: Waiting for rate limit to reset...');
  console.log('Waiting 65 seconds for rate limit window to reset...');
  
  for (let i = 65; i > 0; i--) {
    process.stdout.write(`\rWaiting... ${i} seconds remaining`);
    await sleep(1000);
  }
  console.log('\n');
  
  // Test recovery
  console.log('Step 3: Testing recovery...');
  const recoveryResult = await makeRequest(true, 5000);
  
  if (recoveryResult.status !== 429) {
    console.log('✅ Rate limit recovery successful!');
    console.log(`   Status: ${recoveryResult.status}`);
  } else {
    console.log('❌ Rate limit recovery failed');
    console.log(`   Still getting 429: ${recoveryResult.data.error}`);
  }
  
  console.log('\n' + '='.repeat(60) + '\n');
}

async function testConcurrentRequests() {
  console.log('⚡ Testing Concurrent Requests...\n');
  console.log('Sending 20 concurrent requests to test rate limiting under load...\n');
  
  const promises = [];
  const startTime = Date.now();
  
  // Send 20 concurrent requests
  for (let i = 1; i <= 20; i++) {
    promises.push(
      makeRequest(true, 5000).then(result => ({
        requestId: i,
        ...result
      }))
    );
  }
  
  const results = await Promise.all(promises);
  const endTime = Date.now();
  
  // Analyze results
  let successCount = 0;
  let rateLimitCount = 0;
  let errorCount = 0;
  
  results.forEach(result => {
    if (result.status === 429) {
      rateLimitCount++;
    } else if (result.status === 500 || result.status === 200) {
      successCount++;
    } else {
      errorCount++;
    }
  });
  
  console.log('📊 Concurrent Request Test Results:');
  console.log(`   Total requests: ${results.length}`);
  console.log(`   Successful: ${successCount}`);
  console.log(`   Rate limited: ${rateLimitCount}`);
  console.log(`   Errors: ${errorCount}`);
  console.log(`   Total time: ${endTime - startTime}ms`);
  console.log(`   Average time per request: ${Math.round((endTime - startTime) / results.length)}ms`);
  
  if (rateLimitCount > 0) {
    console.log('✅ Rate limiting works under concurrent load!');
  } else {
    console.log('⚠️  No rate limiting detected under concurrent load');
  }
  
  console.log('\n' + '='.repeat(60) + '\n');
}

async function main() {
  console.log('🧪 Telegram Proxy Service - Rate Limiting Tests\n');
  console.log('This script will test various rate limiting scenarios:\n');
  console.log('1. Global IP rate limiting');
  console.log('2. API key rate limiting');
  console.log('3. Rate limit recovery');
  console.log('4. Concurrent request handling\n');
  
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('Usage: node test-rate-limit.js [options]');
    console.log('Options:');
    console.log('  --global-only    Test only global rate limiting');
    console.log('  --apikey-only    Test only API key rate limiting');
    console.log('  --recovery-only  Test only rate limit recovery');
    console.log('  --concurrent-only Test only concurrent requests');
    console.log('  --help, -h       Show this help message');
    return;
  }
  
  try {
    // Test health first
    console.log('🏥 Checking service health...');
    const healthResult = await makeRequest(false, 2000);
    if (healthResult.status !== 401) { // We expect 401 without API key
      console.log('❌ Service may not be running properly');
      console.log('   Make sure the proxy service is running on port 3009');
      return;
    }
    console.log('✅ Service is running\n');
    
    if (args.includes('--global-only')) {
      await testGlobalRateLimit();
    } else if (args.includes('--apikey-only')) {
      await testApiKeyRateLimit();
    } else if (args.includes('--recovery-only')) {
      await testRateLimitRecovery();
    } else if (args.includes('--concurrent-only')) {
      await testConcurrentRequests();
    } else {
      // Run all tests
      await testGlobalRateLimit();
      await testApiKeyRateLimit();
      await testConcurrentRequests();
      
      console.log('⚠️  Skipping recovery test (takes 65+ seconds)');
      console.log('   Run with --recovery-only to test rate limit recovery');
    }
    
    console.log('🏁 Rate limiting tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
