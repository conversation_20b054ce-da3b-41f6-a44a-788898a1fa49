#!/usr/bin/env node

/**
 * Test with a real bot token (if available)
 * This script tests the proxy with a real Telegram bot
 */

const axios = require('axios');

const PROXY_BASE_URL = 'http://localhost:3009';
const TEST_API_KEY = 'tgp_gZOixCEGIhysLomAC7Bla9RKBnZC0o1M';

// You can set a real bot token here for testing
const REAL_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN || null;

async function testWithRealBot() {
  if (!REAL_BOT_TOKEN) {
    console.log('⚠️  No real bot token provided');
    console.log('   Set TELEGRAM_BOT_TOKEN environment variable to test with real bot');
    console.log('   Example: TELEGRAM_BOT_TOKEN=123456:ABC-DEF node test-real-bot.js');
    return;
  }
  
  console.log('🤖 Testing with real bot token...\n');
  
  try {
    // Test getMe endpoint
    console.log('1. Testing getMe endpoint...');
    const getMeResponse = await axios.get(
      `${PROXY_BASE_URL}/api/v1.0/proxy/bot${REAL_BOT_TOKEN}/getMe`,
      {
        headers: { 'X-API-Key': TEST_API_KEY },
        timeout: 10000
      }
    );
    
    console.log('✅ getMe successful!');
    console.log('   Bot info:', {
      id: getMeResponse.data.result.id,
      username: getMeResponse.data.result.username,
      first_name: getMeResponse.data.result.first_name
    });
    
    // Test getUpdates endpoint
    console.log('\n2. Testing getUpdates endpoint...');
    const updatesResponse = await axios.get(
      `${PROXY_BASE_URL}/api/v1.0/proxy/bot${REAL_BOT_TOKEN}/getUpdates`,
      {
        headers: { 'X-API-Key': TEST_API_KEY },
        timeout: 10000
      }
    );
    
    console.log('✅ getUpdates successful!');
    console.log('   Updates count:', updatesResponse.data.result.length);
    
    console.log('\n🎉 Real bot testing completed successfully!');
    console.log('   The proxy is working correctly with real Telegram API');
    
  } catch (error) {
    if (error.response) {
      console.log('❌ Request failed:');
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    } else {
      console.log('❌ Network error:', error.message);
    }
  }
}

async function testFakeBot() {
  console.log('🧪 Testing with fake bot token (expected to fail)...\n');
  
  try {
    const response = await axios.get(
      `${PROXY_BASE_URL}/api/v1.0/proxy/bot123456:FAKE_TOKEN/getMe`,
      {
        headers: { 'X-API-Key': TEST_API_KEY },
        timeout: 5000
      }
    );
    
    console.log('⚠️  Unexpected success:', response.data);
    
  } catch (error) {
    if (error.response && error.response.status === 500) {
      console.log('✅ Expected failure with fake token');
      console.log('   Status:', error.response.status);
      if (error.response.data.error_code === 401) {
        console.log('   Telegram error: Unauthorized (expected)');
      }
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }
}

async function main() {
  console.log('🚀 Telegram Proxy - Real Bot Testing\n');
  
  // Test health first
  try {
    await axios.get(`${PROXY_BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ Proxy service is running\n');
  } catch (error) {
    console.log('❌ Proxy service is not running');
    process.exit(1);
  }
  
  // Test with fake bot first
  await testFakeBot();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test with real bot if token provided
  await testWithRealBot();
  
  console.log('\n📊 Check usage statistics with:');
  console.log('   node scripts/manage-api-keys.js stats tgp_gZOixCEGIhysLomAC7Bla9RKBnZC0o1M');
}

main();
