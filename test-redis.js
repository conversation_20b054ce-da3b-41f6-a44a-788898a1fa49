#!/usr/bin/env node

/**
 * Test Redis connection for rate limiting
 */

// Setup global variables like in main app
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.logger = Logger(`${__dirname}/logs`);

const redisConnections = require('./lib/connections/redis');
const { RateLimiterRedis } = require('rate-limiter-flexible');

async function testRedisConnection() {
  console.log('🔌 Testing Redis connection...\n');
  
  try {
    // Test basic Redis connection
    const redisClient = redisConnections('master').getConnection();
    console.log('Redis client:', typeof redisClient);
    
    // Test ping
    const pong = await redisClient.ping();
    console.log('✅ Redis ping:', pong);
    
    // Test set/get
    await redisClient.set('test_key', 'test_value');
    const value = await redisClient.get('test_key');
    console.log('✅ Redis set/get test:', value);
    
    // Test rate limiter
    console.log('\n🚦 Testing Rate Limiter...');
    
    const rateLimiter = new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'test_rl',
      points: 5, // 5 requests
      duration: 60, // per 60 seconds
      blockDuration: 60, // block for 60 seconds
    });
    
    console.log('Rate limiter created successfully');
    
    // Test rate limiting
    for (let i = 1; i <= 7; i++) {
      try {
        await rateLimiter.consume('test_ip');
        console.log(`✅ Request ${i}: Allowed`);
      } catch (rejRes) {
        console.log(`❌ Request ${i}: Rate limited`);
        console.log(`   Remaining time: ${Math.round(rejRes.msBeforeNext / 1000)}s`);
        break;
      }
    }
    
    console.log('\n✅ Redis and Rate Limiter are working!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
  
  process.exit(0);
}

testRedisConnection();
