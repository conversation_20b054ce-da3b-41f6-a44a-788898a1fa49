#!/usr/bin/env node

/**
 * Simple Credit Addition Test
 * Test adding credits without complex transactions
 */

const axios = require('axios');

const PROXY_BASE_URL = 'http://localhost:3009';
const TEST_API_KEY = 'tgp_xGQBzBi7ULzKmcbymCfKne80dAHFaHyF';
const USER_ID = '6836bf0bc41564e78fb7b611';

async function testSimpleCreditAddition() {
  console.log('🧪 Testing Simple Credit Addition\n');

  try {
    // 1. Check current balance
    console.log('1. Checking current credit balance...');
    const balanceResponse = await axios.get(`${PROXY_BASE_URL}/credits`, {
      headers: { 'X-API-Key': TEST_API_KEY },
      timeout: 5000
    });
    
    const currentCredits = balanceResponse.data.data.credits.total;
    console.log(`   Current Credits: ${currentCredits}`);
    console.log('');

    // 2. Add credits via MongoDB directly
    console.log('2. Adding 5000 credits via MongoDB...');
    const { exec } = require('child_process');
    
    const mongoCommand = `mongosh "mongodb://************:27227/telegram-proxy" --eval "
      const result = db.users.updateOne(
        {_id: ObjectId('${USER_ID}')}, 
        {\\$inc: {adCreditBalance: 5000, totalCreditsEarned: 5000}, \\$set: {updatedAt: Date.now()}}
      );
      console.log('Update result:', result.modifiedCount, 'documents modified');
    "`;
    
    await new Promise((resolve, reject) => {
      exec(mongoCommand, (error, stdout, stderr) => {
        if (error) {
          console.error('MongoDB update error:', error);
          reject(error);
        } else {
          console.log('   MongoDB output:', stdout);
          resolve();
        }
      });
    });
    
    console.log('');

    // 3. Check new balance
    console.log('3. Checking new credit balance...');
    const newBalanceResponse = await axios.get(`${PROXY_BASE_URL}/credits`, {
      headers: { 'X-API-Key': TEST_API_KEY },
      timeout: 5000
    });
    
    const newCredits = newBalanceResponse.data.data.credits.total;
    const addedCredits = newCredits - currentCredits;
    
    console.log(`   New Credits: ${newCredits}`);
    console.log(`   Added Credits: ${addedCredits}`);
    
    if (addedCredits === 5000) {
      console.log('✅ Credit addition successful!');
    } else {
      console.log('❌ Credit addition failed or partial');
    }
    console.log('');

    // 4. Test API request with credits
    console.log('4. Testing API request with sufficient credits...');
    try {
      const response = await axios.get(`${PROXY_BASE_URL}/bot123456:test/getMe`, {
        headers: { 'X-API-Key': TEST_API_KEY },
        timeout: 3000
      });
      
      console.log('✅ API request processed (unexpected success)');
      
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        console.log('⚠️  API request timeout (expected with fake token)');
        console.log('   This means credit system allowed the request');
      } else if (error.response?.status === 402) {
        console.log('❌ Still getting 402 Payment Required');
      } else {
        console.log(`⚠️  API request error: ${error.response?.status || error.message}`);
      }
    }
    console.log('');

    // 5. Check final balance
    console.log('5. Checking final credit balance...');
    const finalBalanceResponse = await axios.get(`${PROXY_BASE_URL}/credits`, {
      headers: { 'X-API-Key': TEST_API_KEY },
      timeout: 5000
    });
    
    const finalCredits = finalBalanceResponse.data.data.credits.total;
    console.log(`   Final Credits: ${finalCredits}`);
    
    if (finalCredits < newCredits) {
      console.log(`✅ Credits were deducted! (${newCredits - finalCredits} credits used)`);
    } else {
      console.log('⚠️  No credits were deducted');
    }

    console.log('\n🎉 Simple Credit Addition Test Completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testCreditHeaders() {
  console.log('\n🔍 Testing Credit Headers\n');

  try {
    const response = await axios.get(`${PROXY_BASE_URL}/bot123456:test/getMe`, {
      headers: { 'X-API-Key': TEST_API_KEY },
      timeout: 2000,
      validateStatus: () => true // Accept any status
    });

    console.log(`Response Status: ${response.status}`);
    
    const creditHeaders = {
      'x-credits-available': response.headers['x-credits-available'],
      'x-credits-monthly': response.headers['x-credits-monthly'],
      'x-credits-ad': response.headers['x-credits-ad'],
      'x-credits-required': response.headers['x-credits-required']
    };

    console.log('Credit Headers:');
    Object.entries(creditHeaders).forEach(([header, value]) => {
      console.log(`   ${header}: ${value || 'Not present'}`);
    });

    if (creditHeaders['x-credits-available']) {
      console.log('✅ Credit headers are working!');
    } else {
      console.log('❌ Credit headers missing');
    }

  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      console.log('⚠️  Request timeout (expected)');
    } else {
      console.log(`❌ Error: ${error.message}`);
    }
  }
}

async function main() {
  await testSimpleCreditAddition();
  await testCreditHeaders();
}

main();
