#!/usr/bin/env node

/**
 * Simple Rate Limiting Test
 */

const axios = require('axios');

const PROXY_BASE_URL = 'http://localhost:3009';
const BOT_TOKEN = '123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11';

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function makeRequest() {
  try {
    const response = await axios.get(
      `${PROXY_BASE_URL}/api/v1.0/proxy/bot${BOT_TOKEN}/getMe`,
      { timeout: 3000 }
    );
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      status: error.response ? error.response.status : 0,
      data: error.response ? error.response.data : { error: error.message }
    };
  }
}

async function testSimpleRateLimit() {
  console.log('🧪 Simple Rate Limiting Test\n');
  console.log('Sending 10 requests rapidly to trigger rate limiting...\n');
  
  for (let i = 1; i <= 10; i++) {
    const result = await makeRequest();
    
    console.log(`Request ${i}: Status ${result.status}`);
    
    if (result.status === 429) {
      console.log('✅ Rate limiting triggered!');
      console.log('Response:', result.data);
      break;
    } else if (result.status === 401) {
      console.log('   Expected 401 (no API key)');
    } else {
      console.log('   Unexpected status:', result.status);
    }
    
    // Very small delay
    await sleep(10);
  }
}

testSimpleRateLimit();
